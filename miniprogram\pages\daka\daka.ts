import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess, handleUserLogin } from '../../utils/auth';
import { db, punchRecordsCollection, UserInfo, UserStats, PunchRecord, getUserStats } from '../../utils/cloud';

interface CalendarDay {
  day: number | string;
  class: string;
  id: number;
  isPunched?: boolean;
}

Page({
  data: {
    safeAreaTop: 0,
    // 今日练习时长
    todayDuration: {
      minutes: 0,
      seconds: '00'
    },
    // 今日是否已打卡
    isPunchedToday: false,
    // 总共打卡天数
    totalDays: 0,
    // 连续打卡天数
    consecutiveDays: 0,
    // 累计练习时长（分钟）
    totalMinutes: 0,
    // 日历相关
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    calendarDays: [] as CalendarDay[],
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false,
    // 统计数据加载状态
    statsLoading: false,
    // 打卡记录
    punchRecords: [] as PunchRecord[],
    // 统计数据缓存
    statsCache: null as UserStats | null,
    statsCacheTime: 0
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this, (isLoggedIn: boolean, userInfo: UserInfo | null) => {
      if (isLoggedIn && userInfo) {
        this.loadUserData();
      } else {
        this.clearUserData();
      }
    });
    this.customCheckLoginStatus();
  },

  onShow() {
    this.customCheckLoginStatus();
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },

  clearUserData() {
    this.setData({
      totalDays: 0,
      consecutiveDays: 0,
      totalMinutes: 0,
      isPunchedToday: false,
      todayDuration: {
        minutes: 0,
        seconds: '00'
      },
      punchRecords: []
    });
    this.generateCalendar(this.data.currentYear, this.data.currentMonth);
  },

  async customCheckLoginStatus() {
    await checkLoginStatus(this);
    if (this.data.isLoggedIn) {
      await this.loadUserData();
    }
    this.initCalendar();
  },

  async showLoginPopup() {
    await handleUserLogin(this);
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
    this.loadUserData();
  },

  // 加载用户数据
  async loadUserData() {
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      console.log('loadUserData: 用户未登录或无用户信息，跳过');
      return;
    }

    try {
      console.log('=== 开始加载用户数据 ===');
      this.setData({ loading: true });

      // 1. 加载统计数据
      await this.loadUserStats();

      // 2. 先加载今日记录，确保今日练习时长正确显示
      await this.loadTodayRecord();

      // 3. 检查今日打卡状态
      this.checkTodayPunchStatus();

      // 4. 加载当前月份的打卡记录
      await this.loadPunchRecords();

      // 5. 生成日历
      this.generateCalendar(this.data.currentYear, this.data.currentMonth);

      console.log('=== 用户数据加载完成 ===');
    } catch (error) {
      console.error('加载用户数据失败', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadUserStats(forceRefresh: boolean = false) {
    try {
      if (!this.data.openid) return;

      const app = getApp<IAppOption>();

      // 优先检查全局缓存
      if (!forceRefresh && app.getGlobalStatsCache) {
        const globalCache = app.getGlobalStatsCache(this.data.openid);
        if (globalCache) {
          this.setData({
            totalDays: globalCache.totalDays || 0,
            consecutiveDays: globalCache.consecutiveDays || 0,
            totalMinutes: Math.round((globalCache.totalDuration || 0) / 60)
          });
          return;
        }
      }

      // 检查本地缓存是否有效（5分钟内）
      const now = Date.now();
      const cacheValidTime = 5 * 60 * 1000; // 5分钟

      if (!forceRefresh && this.data.statsCache && (now - this.data.statsCacheTime) < cacheValidTime) {
        // 使用本地缓存数据
        const stats = this.data.statsCache;
        this.setData({
          totalDays: stats.totalDays || 0,
          consecutiveDays: stats.consecutiveDays || 0,
          totalMinutes: Math.round((stats.totalDuration || 0) / 60)
        });
        return;
      }

      // 显示统计数据加载状态
      this.setData({
        statsLoading: true
      });

      // 检查网络状态
      const networkType = await this.getNetworkType();
      if (networkType === 'none') {
        // 无网络时，尝试使用过期的缓存数据
        if (this.data.statsCache) {
          const stats = this.data.statsCache;
          this.setData({
            totalDays: stats.totalDays || 0,
            consecutiveDays: stats.consecutiveDays || 0,
            totalMinutes: Math.round((stats.totalDuration || 0) / 60)
          });
          wx.showToast({
            title: '网络不可用，显示缓存数据',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 调用getUserStats云函数获取统计数据
      const stats = await getUserStats(this.data.openid);

      if (stats) {
        // 更新本地缓存
        this.setData({
          statsCache: stats,
          statsCacheTime: now,
          totalDays: stats.totalDays || 0,
          consecutiveDays: stats.consecutiveDays || 0,
          totalMinutes: Math.round((stats.totalDuration || 0) / 60)
        });

        // 更新全局缓存
        if (app.setGlobalStatsCache) {
          app.setGlobalStatsCache(this.data.openid, stats);
        }
      } else {
        // 如果获取失败，设置默认值
        this.setData({
          totalDays: 0,
          consecutiveDays: 0,
          totalMinutes: 0
        });
      }
    } catch (error) {
      console.error('加载用户统计数据失败', error);
      // 发生错误时显示提示
      wx.showToast({
        title: '加载统计数据失败',
        icon: 'none',
        duration: 2000
      });
      // 设置默认值
      this.setData({
        totalDays: 0,
        consecutiveDays: 0,
        totalMinutes: 0
      });
    } finally {
      this.setData({
        statsLoading: false
      });
    }
  },

  async loadPunchRecords() {
    try {
      if (!this.data.openid) return;

      const startDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-01`;
      const endDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-31`;

      console.log(`加载打卡记录: openid=${this.data.openid}, 日期范围=${startDate} 到 ${endDate}`);

      try {
        const result = await punchRecordsCollection
          .where({
            _openid: this.data.openid,
            date: db.command.gte(startDate).and(db.command.lte(endDate))
          })
          .get();

        console.log(`加载到${result.data.length}条打卡记录:`, result.data);

        this.setData({
          punchRecords: result.data as PunchRecord[]
        });
      } catch (dbError: any) {
        if (dbError && dbError.errCode === -502005) {
          console.warn('打卡记录集合不存在，将使用空数组');
          this.setData({
            punchRecords: []
          });
          wx.showToast({
            title: '首次使用，请先完成打卡',
            icon: 'none',
            duration: 2000
          });
        } else {
          throw dbError;
        }
      }
    } catch (error) {
      console.error('加载打卡记录失败', error);
      this.setData({
        punchRecords: []
      });
    }
  },

  checkTodayPunchStatus() {
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    console.log(`=== 检查今日打卡状态 ===`);
    console.log(`当前时间: ${today.toString()}`);
    console.log(`生成的今日日期字符串: ${todayStr}`);
    console.log(`当前打卡记录数量: ${this.data.punchRecords.length}`);
    console.log('所有打卡记录:', this.data.punchRecords);

    // 详细检查每条记录的日期匹配
    this.data.punchRecords.forEach((record, index) => {
      console.log(`记录${index}: date="${record.date}", 是否匹配今日: ${record.date === todayStr}`);
    });

    const todayRecord = this.data.punchRecords.find(record => record.date === todayStr);
    console.log('今日打卡记录:', todayRecord);

    if (todayRecord) {
      // duration现在存储的是秒数，格式化为优化的显示格式
      const totalSeconds = todayRecord.duration; // duration现在是秒数
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;

      // 只有达到10分钟（600秒）才算已打卡
      const isPunchedToday = totalSeconds >= 600;

      console.log(`今日练习时长: ${totalSeconds}秒 = ${minutes}分${seconds}秒, 是否完成打卡: ${isPunchedToday}`);

      this.setData({
        isPunchedToday: isPunchedToday,
        todayDuration: {
          minutes: minutes,
          seconds: String(seconds).padStart(2, '0')
        }
      });
    } else {
      console.log('今日没有打卡记录');
      this.setData({
        isPunchedToday: false,
        todayDuration: {
          minutes: 0,
          seconds: '00'
        }
      });
    }
  },

  initCalendar() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    this.generateCalendar(year, month);
  },
  
  generateCalendar(year: number, month: number) {
    const firstDay = new Date(year, month - 1, 1).getDay();
    const totalDays = new Date(year, month, 0).getDate();
    const today = new Date();
    const isCurrentMonth = year === today.getFullYear() && month === today.getMonth() + 1;
    const todayDate = today.getDate();
    const days: CalendarDay[] = [];

    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: '',
        class: 'day empty',
        id: -i
      });
    }

    for (let i = 1; i <= totalDays; i++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
      // 只有达到10分钟（600秒）的记录才算已打卡
      const record = this.data.punchRecords.find(record => record.date === dateStr);
      const isPunched = this.data.isLoggedIn && record && record.duration >= 600;
      const isToday = isCurrentMonth && i === todayDate;

      let className = 'day';
      if (isPunched) className += ' punched';
      if (isToday) className += ' today';

      days.push({
        day: i,
        class: className,
        id: i,
        isPunched
      });
    }

    this.setData({
      calendarDays: days
    });
  },
  
  async prevMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 1) {
      currentYear--;
      currentMonth = 12;
    } else {
      currentMonth--;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  async nextMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 12) {
      currentYear++;
      currentMonth = 1;
    } else {
      currentMonth++;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      this.loadUserData().then(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  },

  // 刷新统计数据（强制刷新，不使用缓存）
  async refreshUserStats() {
    await this.loadUserStats(true);
  },

  // 加载今日打卡记录（确保今日练习时长正确显示）
  async loadTodayRecord() {
    try {
      if (!this.data.openid) {
        console.log('loadTodayRecord: 没有openid，跳过');
        return;
      }

      const today = new Date();
      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

      console.log(`=== 加载今日记录 ===`);
      console.log(`当前时间: ${today.toString()}`);
      console.log(`openid: ${this.data.openid}`);
      console.log(`查询日期: ${todayStr}`);

      const result = await punchRecordsCollection
        .where({
          _openid: this.data.openid,
          date: todayStr
        })
        .get();

      console.log(`今日记录查询结果:`, result.data);

      if (result.data.length > 0) {
        const todayRecord = result.data[0] as PunchRecord;
        // 更新或添加今日记录到punchRecords中
        const existingRecords = this.data.punchRecords.filter(record => record.date !== todayStr);
        existingRecords.push(todayRecord);

        this.setData({
          punchRecords: existingRecords
        });

        console.log('今日记录已更新到punchRecords中');
      }
    } catch (error) {
      console.error('加载今日记录失败', error);
    }
  },

  // 打卡完成后的回调（供其他页面调用）
  async onPunchCompleted() {
    console.log('打卡完成回调被调用，开始刷新数据');

    try {
      // 先加载今日记录，确保今日练习时长正确显示
      await this.loadTodayRecord();

      // 重新检查今日打卡状态
      this.checkTodayPunchStatus();

      // 重新加载当前月份的打卡记录
      await this.loadPunchRecords();

      // 重新生成日历
      this.generateCalendar(this.data.currentYear, this.data.currentMonth);

      // 刷新统计数据
      await this.refreshUserStats();

      console.log('打卡数据刷新完成');
    } catch (error) {
      console.error('刷新打卡数据失败', error);
    }
  },

  // 获取网络状态
  async getNetworkType(): Promise<string> {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve(res.networkType);
        },
        fail: () => {
          resolve('unknown');
        }
      });
    });
  }
})