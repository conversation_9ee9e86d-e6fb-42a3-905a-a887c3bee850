const CLOUD_ENV = 'cloud1-5go7er9y38b6f1bd';

wx.cloud.init({
  env: CLOUD_ENV,
  traceUser: true
});

export const db = wx.cloud.database();
export const userCollection = db.collection('users');
export const punchRecordsCollection = db.collection('punchRecords');
export const rankingsCollection = db.collection('rankings');

export interface UserInfo {
  avatarUrl: string;
  nickName: string;
  signature: string;
  userId?: string;
  gender?: string;
  age?: number;
}

export interface UserStats {
  totalDays: number;
  consecutiveDays: number;
  totalDuration: number;
}

export interface CloudFunctionResult {
  result?: {
    openid?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface PunchRecord {
  _id?: string;
  _openid?: string;
  date: string;
  duration: number;
  createdAt?: any;
}

export interface RankingRecord {
  _id?: string;
  type: 'total' | 'consecutive' | 'duration'; // 排行榜类型
  rank: number; // 排名
  openid: string; // 用户openid
  userId: string; // 用户ID
  nickName: string; // 用户昵称
  avatarUrl: string; // 用户头像
  value: number; // 排行榜数值（天数或分钟数）
  updatedAt: any; // 更新时间
}

export interface UserRankInfo {
  rank: number;
  nickname: string;
  userId: string;
  avatar: string;
  value: number;
}

export const getOpenid = async (): Promise<string | null> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getOpenid'
    }) as CloudFunctionResult;
    return res.result?.openid || null;
  } catch (error) {
    console.error('获取openid失败', error);
    return null;
  }
};

export const getUserInfoFromCloud = async (openid: string): Promise<UserInfo | null> => {
  try {
    const queryResult = await userCollection.where({
      _openid: openid
    }).get();
    return queryResult.data.length > 0 ? queryResult.data[0] as UserInfo : null;
  } catch (error) {
    console.error('获取用户信息失败', error);
    return null;
  }
};

export const generateUserId = async (): Promise<string> => {
  try {
    const result = await userCollection.orderBy('userId', 'desc').limit(1).get();
    if (result.data.length > 0) {
      const lastUserId = result.data[0].userId || '00000000';
      const nextId = parseInt(lastUserId) + 1;
      return nextId.toString().padStart(8, '0');
    }
    return '00000001';
  } catch (error) {
    console.error('生成用户ID失败', error);
    return Date.now().toString().slice(-8).padStart(8, '0');
  }
};

export const getUserStats = async (openid: string): Promise<UserStats | null> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getUserStats',
      data: { openid }
    }) as CloudFunctionResult;
    return res.result as UserStats || null;
  } catch (error) {
    console.error('获取用户统计数据失败', error);
    return null;
  }
};

export const savePunchRecord = async (date: string, duration: number): Promise<boolean> => {
  try {
    console.log(`调用savePunchRecord云函数: date=${date}, duration=${duration}`);
    const res = await wx.cloud.callFunction({
      name: 'savePunchRecord',
      data: { date, duration }
    }) as CloudFunctionResult;

    console.log('savePunchRecord云函数返回结果:', res.result);

    if (res.result?.success) {
      console.log('打卡记录保存成功:', res.result.message);
      return true;
    } else {
      console.error('打卡记录保存失败:', res.result?.error);
      return false;
    }
  } catch (error) {
    console.error('调用savePunchRecord云函数失败', error);
    return false;
  }
};

export const getRanking = async (type: string = 'total', limit: number = 50): Promise<any[]> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getRanking',
      data: { type, limit }
    }) as CloudFunctionResult;

    if (res.result?.success) {
      return res.result.data || [];
    } else {
      console.error('获取排行榜失败:', res.result?.error);
      return [];
    }
  } catch (error) {
    console.error('调用getRanking云函数失败', error);
    return [];
  }
};

export const getUserRank = async (type: string = 'total', openid?: string): Promise<UserRankInfo | null> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getUserRank',
      data: { type, openid }
    }) as CloudFunctionResult;

    if (res.result?.success) {
      return {
        rank: res.result.rank,
        nickname: res.result.nickname,
        userId: res.result.userId,
        avatar: res.result.avatar,
        value: res.result.value
      };
    } else {
      console.error('获取用户排名失败:', res.result?.error);
      return null;
    }
  } catch (error) {
    console.error('调用getUserRank云函数失败', error);
    return null;
  }
};

export const updateRankings = async (types?: string[]): Promise<boolean> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'updateRankings',
      data: { types }
    }) as CloudFunctionResult;

    return res.result?.success || false;
  } catch (error) {
    console.error('调用updateRankings云函数失败', error);
    return false;
  }
};

export const refreshRankingAfterPunch = async (openid?: string): Promise<boolean> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'refreshRankingAfterPunch',
      data: { openid }
    }) as CloudFunctionResult;

    return res.result?.success || false;
  } catch (error) {
    console.error('调用refreshRankingAfterPunch云函数失败', error);
    return false;
  }
};
