// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const userCollection = db.collection('users')
const punchRecordsCollection = db.collection('punchRecords')
const rankingsCollection = db.collection('rankings')

// 云函数入口函数
exports.main = async (event, context) => {
  const { types = ['total', 'consecutive', 'duration'] } = event
  
  console.log('开始更新排行榜:', types)
  
  try {
    const results = {}
    
    for (const type of types) {
      console.log(`更新${type}排行榜`)
      const result = await updateRankingByType(type)
      results[type] = result
    }
    
    console.log('排行榜更新完成:', results)
    
    return {
      success: true,
      message: '排行榜更新成功',
      results: results
    }
  } catch (error) {
    console.error('更新排行榜失败', error)
    return {
      success: false,
      error: error.message || '更新排行榜失败'
    }
  }
}

// 按类型更新排行榜
async function updateRankingByType(type) {
  try {
    // 获取所有用户
    const allUsersResult = await userCollection.get()
    const allUsers = allUsersResult.data
    
    console.log(`开始计算${allUsers.length}个用户的${type}排行榜`)
    
    // 计算所有用户的统计数据
    const userStats = []
    
    for (const user of allUsers) {
      try {
        const stats = await calculateUserStats(user._openid)
        
        let value = 0
        switch (type) {
          case 'total':
            value = stats.totalDays
            break
          case 'consecutive':
            value = stats.consecutiveDays
            break
          case 'duration':
            value = Math.round(stats.totalDuration / 60) // 转换为分钟
            break
        }
        
        // 只包含有数据的用户
        if (value > 0) {
          userStats.push({
            openid: user._openid,
            userId: user.userId || '',
            nickName: user.nickName || '匿名用户',
            avatarUrl: user.avatarUrl || '/images/default-avatar.png',
            value: value
          })
        }
      } catch (error) {
        console.error(`计算用户${user._openid}统计数据失败:`, error)
        // 继续处理其他用户
      }
    }
    
    // 按数值降序排序
    userStats.sort((a, b) => b.value - a.value)
    
    // 只取前50名
    const top50 = userStats.slice(0, 50)
    
    console.log(`${type}排行榜计算完成，前50名用户数量: ${top50.length}`)
    
    // 清除旧的排行榜数据
    await rankingsCollection.where({
      type: type
    }).remove()
    
    // 批量插入新的排行榜数据
    if (top50.length > 0) {
      const rankingRecords = top50.map((user, index) => ({
        type: type,
        rank: index + 1,
        openid: user.openid,
        userId: user.userId,
        nickName: user.nickName,
        avatarUrl: user.avatarUrl,
        value: user.value,
        updatedAt: db.serverDate()
      }))
      
      // 分批插入（每批最多20条）
      const batchSize = 20
      for (let i = 0; i < rankingRecords.length; i += batchSize) {
        const batch = rankingRecords.slice(i, i + batchSize)
        await Promise.all(batch.map(record => 
          rankingsCollection.add({ data: record })
        ))
      }
    }
    
    return {
      success: true,
      type: type,
      count: top50.length,
      message: `${type}排行榜更新成功，共${top50.length}条记录`
    }
  } catch (error) {
    console.error(`更新${type}排行榜失败:`, error)
    return {
      success: false,
      type: type,
      error: error.message
    }
  }
}

// 计算用户统计数据（复用getUserStats中的逻辑）
async function calculateUserStats(openid) {
  try {
    const punchRecords = await punchRecordsCollection
      .where({
        _openid: openid
      })
      .orderBy('date', 'asc')
      .get()

    if (punchRecords.data.length === 0) {
      return {
        totalDays: 0,
        consecutiveDays: 0,
        totalDuration: 0
      }
    }

    const records = punchRecords.data
    
    // 计算总天数（去重日期）
    const uniqueDates = new Set(records.map(record => record.date))
    const totalDays = uniqueDates.size
    
    // 计算总时长（秒）
    const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    
    // 计算连续天数
    const consecutiveDays = calculateConsecutiveDays(Array.from(uniqueDates).sort())
    
    return {
      totalDays,
      consecutiveDays,
      totalDuration
    }
  } catch (error) {
    console.error('计算用户统计数据失败', error)
    throw error
  }
}

// 计算连续打卡天数
function calculateConsecutiveDays(sortedDates) {
  if (sortedDates.length === 0) return 0
  
  const today = new Date()
  const todayStr = formatDate(today)
  const yesterdayStr = formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000))
  
  // 检查今天或昨天是否有打卡记录
  const hasRecentPunch = sortedDates.includes(todayStr) || sortedDates.includes(yesterdayStr)
  if (!hasRecentPunch) {
    return 0
  }
  
  let consecutiveDays = 0
  let currentDate = new Date(today)
  
  // 从今天开始往前计算连续天数
  while (true) {
    const dateStr = formatDate(currentDate)
    if (sortedDates.includes(dateStr)) {
      consecutiveDays++
      currentDate.setDate(currentDate.getDate() - 1)
    } else {
      break
    }
  }
  
  return consecutiveDays
}

// 格式化日期为 YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
