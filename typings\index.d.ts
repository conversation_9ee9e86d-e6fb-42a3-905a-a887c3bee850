/// <reference path="./types/index.d.ts" />
/// <reference path="./wx-api.d.ts" />

interface UserInfo {
  avatarUrl: string;
  nickName: string;
  signature: string;
  userId?: string;
  totalDays?: number;
  consecutiveDays?: number;
  totalDuration?: number;
  gender?: string;
  age?: number;
}

interface IAppOption {
  globalData: {
    isLoggedIn: boolean;
    userInfo: UserInfo | null;
    openid: string;
    loginCallbacks: Array<(isLoggedIn: boolean, userInfo: UserInfo | null) => void>;
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
  setGlobalLoginStatus?: (isLoggedIn: boolean, userInfo: UserInfo | null, openid: string) => void;
  onLoginStatusChange?: (callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) => void;
  offLoginStatusChange?: (callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) => void;
}