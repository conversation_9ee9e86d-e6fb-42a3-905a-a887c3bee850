<!--打卡页面-->
<view class="container">
  <!-- 胶囊按钮安全区域 -->
  <view class="capsule-safe-area" style="height: {{safeAreaTop}}px;"></view>
  
  <view class="header p-6">
    <view class="font-bold text-dark" style="font-size: 48rpx;">我的打卡</view>
  </view>

  <scroll-view class="content" scroll-y>
    <view class="p-6 pt-0">
      <!-- 今日练习时长卡片 -->
      <view class="today-card bg-white p-6 rounded-xl shadow-sm text-center mb-6">
        <view wx:if="{{loading}}" class="loading-card">
          <view class="loading-spinner"></view>
          <view class="text-gray" style="font-size: 28rpx; margin-top: 20rpx;">加载中...</view>
        </view>
        <view wx:elif="{{!isLoggedIn}}" class="login-prompt-card">
          <view class="text-gray" style="font-size: 32rpx; margin-bottom: 40rpx;">请先登录查看打卡数据</view>
          <button class="login-btn" bindtap="showLoginPopup">微信登录</button>
        </view>
        <view wx:else>
          <view class="text-gray" style="font-size: 32rpx; margin-bottom: 20rpx;">今日练习时长</view>
          <view class="duration-display">
            <text class="duration-number">{{todayDuration.minutes}}</text>
            <text class="duration-unit">分钟</text>
            <text class="duration-number">{{todayDuration.seconds}}</text>
            <text class="duration-unit">秒</text>
          </view>
          <view class="{{isPunchedToday ? 'punch-status punched' : 'punch-status unpunched'}}">
            {{isPunchedToday ? '今日已打卡' : '未完成打卡'}}
          </view>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="stat-grid mb-8">
        <view class="stat-item">
          <view wx:if="{{statsLoading && isLoggedIn}}" class="stat-loading">
            <view class="loading-dot"></view>
          </view>
          <view wx:else class="font-bold text-primary" style="font-size: 48rpx;">{{isLoggedIn ? totalDays : '--'}}</view>
          <view class="text-gray" style="font-size: 24rpx;">总共打卡天数</view>
        </view>
        <view class="stat-item">
          <view wx:if="{{statsLoading && isLoggedIn}}" class="stat-loading">
            <view class="loading-dot"></view>
          </view>
          <view wx:else class="font-bold text-primary" style="font-size: 48rpx;">{{isLoggedIn ? consecutiveDays : '--'}}</view>
          <view class="text-gray" style="font-size: 24rpx;">连续打卡天数</view>
        </view>
        <view class="stat-item">
          <view wx:if="{{statsLoading && isLoggedIn}}" class="stat-loading">
            <view class="loading-dot"></view>
          </view>
          <view wx:else class="font-bold text-primary" style="font-size: 48rpx;">{{isLoggedIn ? totalMinutes : '--'}}</view>
          <view class="text-gray" style="font-size: 24rpx;">累计练习(分钟)</view>
        </view>
      </view>

      <!-- 日历 -->
      <view class="calendar bg-white p-4 rounded-xl shadow-sm">
        <view class="calendar-header flex-row justify-between items-center mb-4">
          <view class="calendar-nav" bindtap="prevMonth">〈</view>
          <view class="calendar-title font-bold text-dark" style="font-size: 32rpx;">{{currentYear}}年 {{currentMonth}}月</view>
          <view class="calendar-nav" bindtap="nextMonth">〉</view>
        </view>

        <view class="calendar-weekdays">
          <view class="weekday" style="font-size: 24rpx;">日</view>
          <view class="weekday" style="font-size: 24rpx;">一</view>
          <view class="weekday" style="font-size: 24rpx;">二</view>
          <view class="weekday" style="font-size: 24rpx;">三</view>
          <view class="weekday" style="font-size: 24rpx;">四</view>
          <view class="weekday" style="font-size: 24rpx;">五</view>
          <view class="weekday" style="font-size: 24rpx;">六</view>
        </view>

        <view class="calendar-days">
          <block wx:for="{{calendarDays}}" wx:key="id">
            <view class="day-wrapper">
              <view class="{{item.class}}" style="font-size: 28rpx;">{{item.day}}</view>
            </view>
          </block>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 登录弹窗 -->
  <login-popup show="{{showLoginPopup}}" bind:save="onLoginSuccess" bind:close="hideLoginPopup"></login-popup>
</view>