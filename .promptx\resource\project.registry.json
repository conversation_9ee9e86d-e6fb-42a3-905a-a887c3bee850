{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-21T07:40:02.791Z", "updatedAt": "2025-07-21T07:40:02.799Z", "resourceCount": 3}, "resources": [{"id": "miniprogram-development-workflow", "source": "project", "protocol": "execution", "name": "Miniprogram Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/execution/miniprogram-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-21T07:40:02.796Z", "updatedAt": "2025-07-21T07:40:02.796Z", "scannedAt": "2025-07-21T07:40:02.796Z", "path": "role/wechat-miniprogram-fullstack/execution/miniprogram-development-workflow.execution.md"}}, {"id": "miniprogram-thinking", "source": "project", "protocol": "thought", "name": "Miniprogram Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/thought/miniprogram-thinking.thought.md", "metadata": {"createdAt": "2025-07-21T07:40:02.798Z", "updatedAt": "2025-07-21T07:40:02.798Z", "scannedAt": "2025-07-21T07:40:02.798Z", "path": "role/wechat-miniprogram-fullstack/thought/miniprogram-thinking.thought.md"}}, {"id": "wechat-miniprogram-fullstack", "source": "project", "protocol": "role", "name": "Wechat Miniprogram Fullstack 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/wechat-miniprogram-fullstack.role.md", "metadata": {"createdAt": "2025-07-21T07:40:02.799Z", "updatedAt": "2025-07-21T07:40:02.799Z", "scannedAt": "2025-07-21T07:40:02.799Z", "path": "role/wechat-miniprogram-fullstack/wechat-miniprogram-fullstack.role.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "thought": 1, "role": 1}, "bySource": {"project": 3}}}