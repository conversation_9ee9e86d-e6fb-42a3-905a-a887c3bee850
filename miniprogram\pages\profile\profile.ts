import { getMenuButtonInfo } from '../../utils/util';
import { db, userCollection, UserInfo } from '../../utils/cloud';

Page({
  data: {
    statusBarHeight: 0,
    navBarHeight: 44,
    safeAreaBottom: 0,
    userInfo: {
      avatarUrl: '/images/tou.png',
      nickName: '',
      signature: '坚持，是一种力量',
      userId: '',
      gender: '',
      age: 0
    } as UserInfo,
    openid: '',
    loading: false,
    genderOptions: ['未设置', '男', '女'],
    genderIndex: 0,
    ageOptions: [] as string[],
    ageIndex: -1
  },

  onLoad() {
    // 获取胶囊按钮信息
    const menuInfo = getMenuButtonInfo();
    this.setData({
      statusBarHeight: menuInfo.statusBarHeight,
      navBarHeight: menuInfo.height,
      safeAreaBottom: menuInfo.safeAreaBottom
    });

    // 初始化年龄选项
    this.initAgeOptions();
    
    // 加载用户信息
    this.loadUserInfo();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 初始化年龄选项
  initAgeOptions() {
    const ageOptions = ['未设置'];
    for (let i = 1; i <= 100; i++) {
      ageOptions.push(`${i}岁`);
    }
    this.setData({ ageOptions });
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 1. 优先检查全局状态（最快）
      const app = getApp<IAppOption>();
      if (app.globalData.isLoggedIn && app.globalData.userInfo) {
        const globalUserInfo = app.globalData.userInfo;

        // 立即显示全局数据，避免加载状态
        const userInfo = {
          avatarUrl: globalUserInfo.avatarUrl || '/images/tou.png',
          nickName: globalUserInfo.nickName || '',
          signature: globalUserInfo.signature || '坚持，是一种力量',
          userId: globalUserInfo.userId || '',
          gender: globalUserInfo.gender || '',
          age: globalUserInfo.age || 0
        };

        // 设置性别和年龄的选择器索引
        const genderIndex = this.data.genderOptions.indexOf(userInfo.gender || '未设置');
        const ageIndex = userInfo.age ? userInfo.age : 0;

        this.setData({
          userInfo,
          genderIndex: genderIndex >= 0 ? genderIndex : 0,
          ageIndex,
          openid: app.globalData.openid
        });

        // 后台静默更新云端数据（不影响UI）
        this.silentUpdateFromCloud();
        return;
      }

      // 2. 检查本地存储（次快）
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo && localUserInfo.nickName) {
        // 立即显示本地数据
        const userInfo = {
          avatarUrl: localUserInfo.avatarUrl || '/images/tou.png',
          nickName: localUserInfo.nickName || '',
          signature: localUserInfo.signature || '坚持，是一种力量',
          userId: localUserInfo.userId || '',
          gender: localUserInfo.gender || '',
          age: localUserInfo.age || 0
        };

        // 设置性别和年龄的选择器索引
        const genderIndex = this.data.genderOptions.indexOf(userInfo.gender || '未设置');
        const ageIndex = userInfo.age ? userInfo.age : 0;

        this.setData({
          userInfo,
          genderIndex: genderIndex >= 0 ? genderIndex : 0,
          ageIndex
        });

        // 后台获取最新数据
        this.updateFromCloud();
        return;
      }

      // 3. 没有任何本地数据，返回上一页
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('加载用户信息失败', error);
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      });
    }
  },

  // 静默更新云端数据（不显示加载状态）
  async silentUpdateFromCloud() {
    try {
      if (!this.data.openid) return;

      const userResult = await userCollection.where({
        _openid: this.data.openid
      }).get();

      if (userResult.data.length > 0) {
        const userData = userResult.data[0];
        const cloudUserInfo = {
          avatarUrl: userData.avatarUrl || '/images/tou.png',
          nickName: userData.nickName || '',
          signature: userData.signature || '坚持，是一种力量',
          userId: userData.userId || '',
          gender: userData.gender || '',
          age: userData.age || 0,
          totalDays: userData.totalDays || 0,
          consecutiveDays: userData.consecutiveDays || 0,
          totalDuration: userData.totalDuration || 0
        };

        // 只有数据真的有变化时才更新UI
        if (JSON.stringify(cloudUserInfo) !== JSON.stringify(this.data.userInfo)) {
          const genderIndex = this.data.genderOptions.indexOf(cloudUserInfo.gender || '未设置');
          const ageIndex = cloudUserInfo.age ? cloudUserInfo.age : 0;

          this.setData({
            userInfo: cloudUserInfo,
            genderIndex: genderIndex >= 0 ? genderIndex : 0,
            ageIndex
          });

          // 更新本地存储
          wx.setStorageSync('userInfo', cloudUserInfo);

          // 更新全局状态
          const app = getApp<IAppOption>();
          if (app.setGlobalLoginStatus) {
            app.setGlobalLoginStatus(true, cloudUserInfo, this.data.openid);
          }
        }
      }
    } catch (error) {
      console.warn('静默更新云端数据失败', error);
      // 静默失败，不影响用户体验
    }
  },

  // 后台更新云端数据
  async updateFromCloud() {
    try {
      // 获取openid
      await this.getOpenid();

      // 从云数据库获取完整用户信息
      const userResult = await userCollection.where({
        _openid: this.data.openid
      }).get();

      if (userResult.data.length > 0) {
        const userData = userResult.data[0];
        const userInfo = {
          avatarUrl: userData.avatarUrl || '/images/tou.png',
          nickName: userData.nickName || '',
          signature: userData.signature || '坚持，是一种力量',
          userId: userData.userId || '',
          gender: userData.gender || '',
          age: userData.age || 0,
          totalDays: userData.totalDays || 0,
          consecutiveDays: userData.consecutiveDays || 0,
          totalDuration: userData.totalDuration || 0
        };

        // 设置性别和年龄的选择器索引
        const genderIndex = this.data.genderOptions.indexOf(userInfo.gender || '未设置');
        const ageIndex = userInfo.age ? userInfo.age : 0;

        this.setData({
          userInfo,
          genderIndex: genderIndex >= 0 ? genderIndex : 0,
          ageIndex
        });

        // 更新本地存储
        wx.setStorageSync('userInfo', userInfo);

        // 更新全局状态
        const app = getApp<IAppOption>();
        if (app.setGlobalLoginStatus) {
          app.setGlobalLoginStatus(true, userInfo, this.data.openid);
        }
      }
    } catch (error) {
      console.warn('后台更新云端数据失败', error);
      // 后台更新失败，不影响当前显示
    }
  },

  // 获取openid
  async getOpenid(): Promise<boolean> {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getOpenid'
      });

      if (res.result && (res.result as any).openid) {
        this.setData({
          openid: (res.result as any).openid
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('获取openid失败', error);
      return false;
    }
  },

  // 生成用户ID
  async generateUserId(): Promise<string> {
    try {
      // 获取当前最大的用户ID
      // 添加查询条件避免全量查询警告
      const result = await userCollection
        .where({
          userId: db.command.exists(true)  // 只查询有userId字段的记录
        })
        .orderBy('userId', 'desc')
        .limit(1)
        .get();

      let nextId = 1;
      if (result.data.length > 0 && result.data[0].userId) {
        const lastId = parseInt(result.data[0].userId);
        nextId = lastId + 1;
      }

      // 格式化为8位数字，前面补0
      return nextId.toString().padStart(8, '0');
    } catch (error) {
      console.error('生成用户ID失败', error);
      // 如果失败，使用时间戳生成
      return Date.now().toString().slice(-8).padStart(8, '0');
    }
  },

  // 选择头像
  async onChooseAvatar(e: any) {
    try {
      const { avatarUrl } = e.detail;
      
      if (!avatarUrl) {
        wx.showToast({
          title: '头像选择失败',
          icon: 'none'
        });
        return;
      }

      // 检查是否在开发者工具中
      const deviceInfo = wx.getDeviceInfo();
      const isDevTool = deviceInfo.platform === 'devtools';
      
      if (isDevTool) {
        // 开发者工具中，使用本地默认头像
        const defaultAvatarUrl = '/images/default-avatar.png';

        const userInfo = this.data.userInfo;
        userInfo.avatarUrl = defaultAvatarUrl;
        this.setData({ userInfo });

        wx.showToast({
          title: '开发环境已选择头像',
          icon: 'success',
          duration: 1000
        });
      } else {
        // 真机环境，上传头像到云存储
        if (avatarUrl.includes('tmp')) {
          try {
            const cloudPath = `avatars/${Date.now()}-${Math.random().toString(36).substring(2, 11)}.jpg`;
            const uploadResult = await wx.cloud.uploadFile({
              cloudPath,
              filePath: avatarUrl
            });
            
            if (uploadResult.fileID) {
              const userInfo = this.data.userInfo;
              userInfo.avatarUrl = uploadResult.fileID;
              this.setData({ userInfo });
            }
          } catch (uploadError) {
            console.warn('云存储上传失败:', uploadError);
            const userInfo = this.data.userInfo;
            userInfo.avatarUrl = avatarUrl;
            this.setData({ userInfo });
          }
        } else {
          const userInfo = this.data.userInfo;
          userInfo.avatarUrl = avatarUrl;
          this.setData({ userInfo });
        }
        
        wx.showToast({
          title: '头像选择成功',
          icon: 'success',
          duration: 1000
        });
      }
    } catch (error) {
      console.error('选择头像失败:', error);
      wx.showToast({
        title: '头像选择失败，请重试',
        icon: 'none'
      });
    }
  },

  // 昵称输入
  onNicknameInput(e: any) {
    const userInfo = this.data.userInfo;
    userInfo.nickName = e.detail.value;
    this.setData({ userInfo });
  },

  // 签名输入
  onSignatureInput(e: any) {
    const userInfo = this.data.userInfo;
    userInfo.signature = e.detail.value;
    this.setData({ userInfo });
  },

  // 性别选择
  onGenderChange(e: any) {
    const genderIndex = parseInt(e.detail.value);
    const gender = this.data.genderOptions[genderIndex];
    const userInfo = this.data.userInfo;
    userInfo.gender = gender === '未设置' ? '' : gender;
    this.setData({ 
      userInfo,
      genderIndex 
    });
  },

  // 年龄选择
  onAgeChange(e: any) {
    const ageIndex = parseInt(e.detail.value);
    const ageStr = this.data.ageOptions[ageIndex];
    const userInfo = this.data.userInfo;
    
    if (ageStr === '未设置') {
      userInfo.age = 0;
    } else {
      userInfo.age = parseInt(ageStr.replace('岁', ''));
    }
    
    this.setData({ 
      userInfo,
      ageIndex 
    });
  },

  // 保存个人资料
  async saveProfile() {
    try {
      // 验证必填项
      if (!this.data.userInfo.nickName.trim()) {
        wx.showToast({
          title: '请输入昵称',
          icon: 'none'
        });
        return;
      }

      if (!this.data.userInfo.signature.trim()) {
        wx.showToast({
          title: '请输入签名',
          icon: 'none'
        });
        return;
      }

      this.setData({ loading: true });

      // 确保有openid
      if (!this.data.openid) {
        await this.getOpenid();
      }

      // 查询用户是否已存在
      const queryResult = await userCollection.where({
        _openid: this.data.openid
      }).get();

      // 如果用户不存在，生成用户ID
      if (queryResult.data.length === 0 || !queryResult.data[0].userId) {
        const userId = await this.generateUserId();
        this.data.userInfo.userId = userId;
      }

      // 构建更新数据
      const updateData: any = {
        avatarUrl: this.data.userInfo.avatarUrl,
        nickName: this.data.userInfo.nickName.trim(),
        signature: this.data.userInfo.signature.trim(),
        gender: this.data.userInfo.gender,
        age: this.data.userInfo.age,
        updatedAt: db.serverDate()
      };

      // 如果有用户ID，添加到更新数据中
      if (this.data.userInfo.userId) {
        updateData.userId = this.data.userInfo.userId;
      }

      if (queryResult.data.length === 0) {
        // 新用户，添加记录
        await userCollection.add({
          data: {
            ...updateData,
            createdAt: db.serverDate()
          }
        });
      } else {
        // 已有用户，更新信息
        const docId = queryResult.data[0]._id;
        if (docId) {
          await userCollection.doc(docId).update({
            data: updateData
          });
        }
      }

      // 更新本地存储
      wx.setStorageSync('userInfo', {
        avatarUrl: this.data.userInfo.avatarUrl,
        nickName: this.data.userInfo.nickName,
        signature: this.data.userInfo.signature
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('保存个人资料失败', error);
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      }
    });
  },

  // 执行退出登录操作
  performLogout() {
    try {
      // 1. 清除本地存储
      wx.removeStorageSync('userInfo');

      // 2. 设置退出登录标记（防止自动重新登录）
      wx.setStorageSync('userLoggedOut', true);

      // 3. 清除全局状态（这是关键！）
      const app = getApp<IAppOption>();
      if (app.setGlobalLoginStatus) {
        app.setGlobalLoginStatus(false, null, '');
      }

      // 4. 清除当前页面状态
      this.setData({
        userInfo: {
          avatarUrl: '/images/tou.png',
          nickName: '',
          signature: '坚持，是一种力量',
          userId: '',
          gender: '',
          age: 0
        },
        openid: '',
        genderIndex: 0,
        ageIndex: -1
      });

      // 5. 显示退出成功提示
      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 1500
      });

      // 6. 延迟返回上一页，让用户看到提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('退出登录失败', error);
      wx.showToast({
        title: '退出登录失败',
        icon: 'none'
      });
    }
  }
});
