# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# WeChat Mini Program
# 微信小程序私有配置文件（包含个人开发者信息）
# project.private.config.json

# 云开发相关
# 云函数本地调试产生的文件
cloudfunctions/**/node_modules/
cloudfunctions/**/.env
cloudfunctions/**/package-lock.json

# 编译输出
dist/
build/
*.map

# 日志文件
logs/
*.log

# 临时文件
.tmp/
.temp/
*.tmp
*.temp

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 配置文件
# .vscode/
.idea/
*.swp
*.swo
*~

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup

# 原型文件目录（如果不需要版本控制）
# phototype/

# TypeScript 编译缓存
*.tsbuildinfo

# 测试覆盖率报告
coverage/
.nyc_output/

# 微信开发者工具生成的文件
.wechat_devtools/

# 云开发配置目录（通常为空或包含敏感信息）
cloudbaserc/

# 云函数模板目录（如果存在）
cloudfunctionTemplate/

# 锁文件（根据团队需要决定是否忽略）
yarn.lock

# 测试文件和文档（根据项目需要）
*.test.*
test_*.md

# 编辑器相关
.cursor/
