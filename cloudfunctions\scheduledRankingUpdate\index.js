// 定时更新排行榜云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 定时更新排行榜数据
 * 每天凌晨2点自动执行
 */
exports.main = async (event, context) => {
  console.log('定时任务开始：更新排行榜数据');
  
  try {
    // 获取所有用户的统计数据
    const usersResult = await db.collection('users').get();
    const users = usersResult.data;
    
    if (users.length === 0) {
      console.log('没有用户数据，跳过排行榜更新');
      return { success: true, message: '没有用户数据' };
    }

    console.log(`获取到 ${users.length} 个用户数据，开始生成排行榜`);

    // 生成三种排行榜数据
    const rankings = {
      total: generateRanking(users, 'totalDays'),
      consecutive: generateRanking(users, 'consecutiveDays'), 
      duration: generateRanking(users, 'totalMinutes')
    };

    // 更新排行榜集合
    const updatePromises = Object.entries(rankings).map(([type, data]) => 
      updateRankingCollection(type, data)
    );

    await Promise.all(updatePromises);

    console.log('定时任务完成：排行榜数据更新成功');
    return { 
      success: true, 
      message: '排行榜更新成功',
      timestamp: new Date().toISOString(),
      userCount: users.length
    };

  } catch (error) {
    console.error('定时任务失败：', error);
    return { 
      success: false, 
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 生成排行榜数据
 */
function generateRanking(users, field) {
  return users
    .filter(user => user[field] > 0) // 只包含有数据的用户
    .map(user => ({
      userId: user.userId,
      nickname: user.nickname || '未设置昵称',
      avatar: user.avatar || '/images/default-avatar.png',
      value: user[field] || 0
    }))
    .sort((a, b) => b.value - a.value) // 降序排列
    .slice(0, 100) // 只保留前100名
    .map((user, index) => ({
      ...user,
      rank: index + 1
    }));
}

/**
 * 更新排行榜集合
 */
async function updateRankingCollection(type, data) {
  const docId = `${type}_rankings`;
  
  try {
    // 尝试更新现有文档
    await db.collection('rankings').doc(docId).update({
      data: {
        type,
        data,
        lastUpdated: new Date()
      }
    });
    console.log(`${type} 排行榜更新成功`);
  } catch (error) {
    if (error.errCode === -502005) {
      // 文档不存在，创建新文档
      await db.collection('rankings').add({
        data: {
          _id: docId,
          type,
          data,
          lastUpdated: new Date()
        }
      });
      console.log(`${type} 排行榜创建成功`);
    } else {
      throw error;
    }
  }
}
