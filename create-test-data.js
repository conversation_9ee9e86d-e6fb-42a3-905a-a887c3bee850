// 创建测试数据的云函数代码
// 在云开发控制台中手动执行，用于快速生成测试数据

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    // 创建测试用户数据
    const testUsers = [
      {
        _openid: 'test_user_001',
        userId: '00000001',
        nickName: '测试用户1',
        avatarUrl: '/images/default-avatar.png',
        signature: '我是测试用户1',
        createdAt: db.serverDate()
      },
      {
        _openid: 'test_user_002', 
        userId: '00000002',
        nickName: '测试用户2',
        avatarUrl: '/images/default-avatar.png',
        signature: '我是测试用户2',
        createdAt: db.serverDate()
      }
    ];

    // 插入测试用户
    for (const user of testUsers) {
      await db.collection('users').add({
        data: user
      });
    }

    // 创建测试打卡记录
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    const testPunchRecords = [
      {
        _openid: 'test_user_001',
        date: formatDate(today),
        duration: 1800, // 30分钟
        createdAt: db.serverDate()
      },
      {
        _openid: 'test_user_001',
        date: formatDate(yesterday),
        duration: 1200, // 20分钟
        createdAt: db.serverDate()
      },
      {
        _openid: 'test_user_002',
        date: formatDate(today),
        duration: 900, // 15分钟
        createdAt: db.serverDate()
      }
    ];

    // 插入测试打卡记录
    for (const record of testPunchRecords) {
      await db.collection('punchRecords').add({
        data: record
      });
    }

    return {
      success: true,
      message: '测试数据创建成功',
      data: {
        users: testUsers.length,
        punchRecords: testPunchRecords.length
      }
    };

  } catch (error) {
    console.error('创建测试数据失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 格式化日期为 YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
