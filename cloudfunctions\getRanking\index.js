// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const rankingsCollection = db.collection('rankings')

// 云函数入口函数
exports.main = async (event, context) => {
  const { type = 'total', limit = 50 } = event
  
  if (!['total', 'consecutive', 'duration'].includes(type)) {
    return {
      success: false,
      error: '无效的排行榜类型'
    }
  }

  try {
    console.log(`获取排行榜: type=${type}, limit=${limit}`)
    
    // 从rankings集合获取预计算的排行榜数据
    const result = await rankingsCollection
      .where({
        type: type
      })
      .orderBy('rank', 'asc')
      .limit(limit)
      .get()

    console.log(`获取到${result.data.length}条排行榜记录`)

    // 转换数据格式
    const rankingList = result.data.map(record => ({
      id: record.rank,
      nickname: record.nickName,
      userId: record.userId,
      avatar: record.avatarUrl,
      value: record.value,
      _openid: record.openid
    }))

    return {
      success: true,
      data: rankingList,
      total: result.data.length
    }
  } catch (error) {
    console.error('获取排行榜失败', error)
    return {
      success: false,
      error: error.message || '获取排行榜失败'
    }
  }
}
