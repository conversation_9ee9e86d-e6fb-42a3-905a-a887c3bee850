/* 打卡页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #F9FAFB;
}

.header {
  padding-bottom: 16rpx;
}

.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.today-card {
  padding: 48rpx;
  border-radius: 24rpx;
}

/* 时长显示样式 */
.duration-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  line-height: 1;
  margin-bottom: 20rpx;
  gap: 8rpx;
}

.duration-number {
  font-size: 80rpx;
  font-weight: bold;
  color: #1F2937;
}

.duration-unit {
  font-size: 32rpx;
  color: #9CA3AF;
  font-weight: normal;
  margin-right: 16rpx;
}

.stat-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  text-align: center;
  gap: 16rpx;
  margin: 40rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.text-primary {
  color: #92400E;
}

.text-gray {
  color: #6B7280;
}

.text-dark {
  color: #1F2937;
}

.punch-status {
  display: inline-block;
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 24rpx;
  border-radius: 100rpx;
  margin-top: 16rpx;
}

.punched {
  background-color: #D1FAE5;
  color: #065F46;
}

.unpunched {
  background-color: #FEE2E2;
  color: #B91C1C;
}

.calendar {
  margin-bottom: 140rpx;
  border-radius: 24rpx;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.calendar-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  font-size: 32rpx;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 16rpx;
}

.weekday {
  color: #9CA3AF;
  padding: 16rpx;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  text-align: center;
}

.day-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8rpx 0;
}

.day {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.empty {
  background: transparent;
}

.punched {
  background-color: #92400E;
  color: white;
}

.today {
  border: 2rpx solid #92400E;
  color: #92400E;
  font-weight: bold;
}

/* 今天且已打卡的样式 - 优先级更高 */
.today.punched {
  background-color: #92400E;
  color: white;
  border: 3rpx solid #F59E0B;
  font-weight: bold;
}

/* 登录提示卡片 */
.login-prompt-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.login-btn {
  background-color: #92400E;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200rpx;
}

.login-btn::after {
  border: none;
}

.login-btn:active {
  background-color: #7C2D12;
  transform: scale(0.98);
}

/* 禁用状态样式（保留用于其他可能的用途） */
.disabled {
  opacity: 0.4;
  pointer-events: none;
}

/* 加载状态样式 */
.loading-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #F3F4F6;
  border-top: 4rpx solid #92400E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 统计数据加载状态 */
.stat-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48rpx;
}

.loading-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #D1D5DB;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}