

## v0.2.1 (2020-04-28)

#### :rocket: Enhancement
* [#252](https://github.com/raszi/node-tmp/pull/252) Closes [#250](https://github.com/raszi/node-tmp/issues/250): introduce tmpdir option for overriding the system tmp dir ([@silkentrance](https://github.com/silkentrance))

#### :house: Internal
* [#253](https://github.com/raszi/node-tmp/pull/253) Closes [#191](https://github.com/raszi/node-tmp/issues/191): generate changelog from pull requests using lerna-changelog ([@silkentrance](https://github.com/silkentrance))

#### Committers: 1
- <PERSON><PERSON> ([@silkentrance](https://github.com/silkentrance))


## v0.2.0 (2020-04-25)

#### :rocket: Enhancement
* [#234](https://github.com/raszi/node-tmp/pull/234) feat: stabilize tmp for v0.2.0 release ([@silkentrance](https://github.com/silkentrance))

#### :bug: Bug Fix
* [#231](https://github.com/raszi/node-tmp/pull/231) Closes [#230](https://github.com/raszi/node-tmp/issues/230): regression after fix for #197 ([@silkentrance](https://github.com/silkentrance))
* [#220](https://github.com/raszi/node-tmp/pull/220) Closes [#197](https://github.com/raszi/node-tmp/issues/197): return sync callback when using the sync interface, otherwise return the async callback ([@silkentrance](https://github.com/silkentrance))
* [#193](https://github.com/raszi/node-tmp/pull/193) Closes [#192](https://github.com/raszi/node-tmp/issues/192): tmp must not exit the process on its own ([@silkentrance](https://github.com/silkentrance))

#### :memo: Documentation
* [#221](https://github.com/raszi/node-tmp/pull/221) Gh 206 document name option ([@silkentrance](https://github.com/silkentrance))

#### :house: Internal
* [#226](https://github.com/raszi/node-tmp/pull/226) Closes [#212](https://github.com/raszi/node-tmp/issues/212): enable direct name option test ([@silkentrance](https://github.com/silkentrance))
* [#225](https://github.com/raszi/node-tmp/pull/225) Closes [#211](https://github.com/raszi/node-tmp/issues/211): existing tests must clean up after themselves ([@silkentrance](https://github.com/silkentrance))
* [#224](https://github.com/raszi/node-tmp/pull/224) Closes [#217](https://github.com/raszi/node-tmp/issues/217): name tests must use tmpName ([@silkentrance](https://github.com/silkentrance))
* [#223](https://github.com/raszi/node-tmp/pull/223) Closes [#214](https://github.com/raszi/node-tmp/issues/214): refactor tests and lib ([@silkentrance](https://github.com/silkentrance))
* [#198](https://github.com/raszi/node-tmp/pull/198) Update dependencies to latest versions ([@matsev](https://github.com/matsev))

#### Committers: 2
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- Mattias Severson ([@matsev](https://github.com/matsev))


## v0.1.0 (2019-03-20)

#### :rocket: Enhancement
* [#177](https://github.com/raszi/node-tmp/pull/177) fix: fail early if there is no tmp dir specified ([@silkentrance](https://github.com/silkentrance))
* [#159](https://github.com/raszi/node-tmp/pull/159) Closes [#121](https://github.com/raszi/node-tmp/issues/121) ([@silkentrance](https://github.com/silkentrance))
* [#161](https://github.com/raszi/node-tmp/pull/161) Closes [#155](https://github.com/raszi/node-tmp/issues/155) ([@silkentrance](https://github.com/silkentrance))
* [#166](https://github.com/raszi/node-tmp/pull/166) fix: avoid relying on Node’s internals ([@addaleax](https://github.com/addaleax))
* [#144](https://github.com/raszi/node-tmp/pull/144) prepend opts.dir || tmpDir to template if no path is given ([@silkentrance](https://github.com/silkentrance))

#### :bug: Bug Fix
* [#183](https://github.com/raszi/node-tmp/pull/183) Closes [#182](https://github.com/raszi/node-tmp/issues/182) fileSync takes empty string postfix option ([@gutte](https://github.com/gutte))
* [#130](https://github.com/raszi/node-tmp/pull/130) Closes [#129](https://github.com/raszi/node-tmp/issues/129) install process listeners safely ([@silkentrance](https://github.com/silkentrance))

#### :memo: Documentation
* [#188](https://github.com/raszi/node-tmp/pull/188) HOTCloses [#187](https://github.com/raszi/node-tmp/issues/187): restore behaviour for #182 ([@silkentrance](https://github.com/silkentrance))
* [#180](https://github.com/raszi/node-tmp/pull/180) fix gh-179: template no longer accepts arbitrary paths ([@silkentrance](https://github.com/silkentrance))
* [#175](https://github.com/raszi/node-tmp/pull/175) docs: add `unsafeCleanup` option to jsdoc ([@kerimdzhanov](https://github.com/kerimdzhanov))
* [#151](https://github.com/raszi/node-tmp/pull/151) docs: fix link to tmp-promise ([@silkentrance](https://github.com/silkentrance))

#### :house: Internal
* [#184](https://github.com/raszi/node-tmp/pull/184) test: add missing tests for #182 ([@silkentrance](https://github.com/silkentrance))
* [#171](https://github.com/raszi/node-tmp/pull/171) chore: drop old NodeJS support ([@poppinlp](https://github.com/poppinlp))
* [#170](https://github.com/raszi/node-tmp/pull/170) chore: update dependencies ([@raszi](https://github.com/raszi))
* [#165](https://github.com/raszi/node-tmp/pull/165) test: add missing tests ([@raszi](https://github.com/raszi))
* [#163](https://github.com/raszi/node-tmp/pull/163) chore: add lint npm task ([@raszi](https://github.com/raszi))
* [#107](https://github.com/raszi/node-tmp/pull/107) chore: add coverage report ([@raszi](https://github.com/raszi))
* [#141](https://github.com/raszi/node-tmp/pull/141) test: refactor tests for mocha ([@silkentrance](https://github.com/silkentrance))
* [#154](https://github.com/raszi/node-tmp/pull/154) chore: change Travis configuration ([@raszi](https://github.com/raszi))
* [#152](https://github.com/raszi/node-tmp/pull/152) fix: drop Node v0.6.0 ([@raszi](https://github.com/raszi))

#### Committers: 6
- Anna Henningsen ([@addaleax](https://github.com/addaleax))
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- Dan Kerimdzhanov ([@kerimdzhanov](https://github.com/kerimdzhanov))
- Gustav Klingstedt ([@gutte](https://github.com/gutte))
- KARASZI István ([@raszi](https://github.com/raszi))
- PoppinL ([@poppinlp](https://github.com/poppinlp))


## v0.0.33 (2017-08-12)

#### :rocket: Enhancement
* [#147](https://github.com/raszi/node-tmp/pull/147) fix: with name option try at most once to get a unique tmp name ([@silkentrance](https://github.com/silkentrance))

#### :bug: Bug Fix
* [#149](https://github.com/raszi/node-tmp/pull/149) fix(fileSync): must honor detachDescriptor and discardDescriptor options ([@silkentrance](https://github.com/silkentrance))
* [#119](https://github.com/raszi/node-tmp/pull/119) Closes [#115](https://github.com/raszi/node-tmp/issues/115) ([@silkentrance](https://github.com/silkentrance))

#### :memo: Documentation
* [#128](https://github.com/raszi/node-tmp/pull/128) Closes [#127](https://github.com/raszi/node-tmp/issues/127) add reference to tmp-promise ([@silkentrance](https://github.com/silkentrance))

#### :house: Internal
* [#135](https://github.com/raszi/node-tmp/pull/135) Closes [#133](https://github.com/raszi/node-tmp/issues/133), #134 ([@silkentrance](https://github.com/silkentrance))
* [#123](https://github.com/raszi/node-tmp/pull/123) docs: update tmp.js MIT license header to 2017 ([@madnight](https://github.com/madnight))
* [#122](https://github.com/raszi/node-tmp/pull/122) chore: add issue template ([@silkentrance](https://github.com/silkentrance))

#### Committers: 2
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- Fabian Beuke ([@madnight](https://github.com/madnight))


## v0.0.32 (2017-03-24)

#### :memo: Documentation
* [#106](https://github.com/raszi/node-tmp/pull/106) doc: add proper JSDoc documentation ([@raszi](https://github.com/raszi))

#### :house: Internal
* [#111](https://github.com/raszi/node-tmp/pull/111) test: add Windows tests ([@binki](https://github.com/binki))
* [#110](https://github.com/raszi/node-tmp/pull/110) chore: add AppVeyor ([@binki](https://github.com/binki))
* [#105](https://github.com/raszi/node-tmp/pull/105) chore: use const where possible ([@raszi](https://github.com/raszi))
* [#104](https://github.com/raszi/node-tmp/pull/104) style: fix various style issues ([@raszi](https://github.com/raszi))

#### Committers: 2
- KARASZI István ([@raszi](https://github.com/raszi))
- Nathan Phillip Brink ([@binki](https://github.com/binki))


## v0.0.31 (2016-11-21)

#### :rocket: Enhancement
* [#99](https://github.com/raszi/node-tmp/pull/99) feat: add next callback functionality ([@silkentrance](https://github.com/silkentrance))
* [#94](https://github.com/raszi/node-tmp/pull/94) feat: add options to control descriptor management ([@pabigot](https://github.com/pabigot))

#### :house: Internal
* [#101](https://github.com/raszi/node-tmp/pull/101) fix: Include files in the package.json ([@raszi](https://github.com/raszi))

#### Committers: 3
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- KARASZI István ([@raszi](https://github.com/raszi))
- Peter A. Bigot ([@pabigot](https://github.com/pabigot))


## v0.0.30 (2016-11-01)

#### :bug: Bug Fix
* [#96](https://github.com/raszi/node-tmp/pull/96) fix: constants for Node 6 ([@jnj16180340](https://github.com/jnj16180340))
* [#98](https://github.com/raszi/node-tmp/pull/98) fix: garbage collector ([@Ari-H](https://github.com/Ari-H))

#### Committers: 2
- Nate Johnson ([@jnj16180340](https://github.com/jnj16180340))
- [@Ari-H](https://github.com/Ari-H)


## v0.0.29 (2016-09-18)

#### :rocket: Enhancement
* [#87](https://github.com/raszi/node-tmp/pull/87) fix: replace calls to deprecated fs API functions ([@OlliV](https://github.com/OlliV))

#### :bug: Bug Fix
* [#70](https://github.com/raszi/node-tmp/pull/70) fix: prune `_removeObjects` correctly ([@joliss](https://github.com/joliss))
* [#71](https://github.com/raszi/node-tmp/pull/71) Fix typo ([@gcampax](https://github.com/gcampax))

#### :memo: Documentation
* [#77](https://github.com/raszi/node-tmp/pull/77) docs: change mkstemps to mkstemp ([@thefourtheye](https://github.com/thefourtheye))

#### :house: Internal
* [#92](https://github.com/raszi/node-tmp/pull/92) chore: add Travis CI support for Node 6 ([@amilajack](https://github.com/amilajack))
* [#79](https://github.com/raszi/node-tmp/pull/79) fix: remove unneeded require statement ([@whmountains](https://github.com/whmountains))

#### Committers: 6
- Amila Welihinda ([@amilajack](https://github.com/amilajack))
- Caleb Whiting ([@whmountains](https://github.com/whmountains))
- Giovanni Campagna ([@gcampax](https://github.com/gcampax))
- Jo Liss ([@joliss](https://github.com/joliss))
- Olli Vanhoja ([@OlliV](https://github.com/OlliV))
- Sakthipriyan Vairamani ([@thefourtheye](https://github.com/thefourtheye))


## v0.0.28 (2015-09-27)

#### :bug: Bug Fix
* [#63](https://github.com/raszi/node-tmp/pull/63) fix: delete for _rmdirRecursiveSync ([@voltrevo](https://github.com/voltrevo))

#### :memo: Documentation
* [#64](https://github.com/raszi/node-tmp/pull/64) docs: fix typo in the README ([@JTKnox91](https://github.com/JTKnox91))

#### :house: Internal
* [#67](https://github.com/raszi/node-tmp/pull/67) test: add node v4.0 v4.1 to travis config ([@raszi](https://github.com/raszi))
* [#66](https://github.com/raszi/node-tmp/pull/66) chore(deps): update deps ([@raszi](https://github.com/raszi))

#### Committers: 3
- Andrew Morris ([@voltrevo](https://github.com/voltrevo))
- John T. Knox ([@JTKnox91](https://github.com/JTKnox91))
- KARASZI István ([@raszi](https://github.com/raszi))


## v0.0.27 (2015-08-15)

#### :bug: Bug Fix
* [#60](https://github.com/raszi/node-tmp/pull/60) fix: unlinking when the file has been already removed ([@silkentrance](https://github.com/silkentrance))

#### :memo: Documentation
* [#55](https://github.com/raszi/node-tmp/pull/55) docs(README): update README ([@raszi](https://github.com/raszi))

#### :house: Internal
* [#56](https://github.com/raszi/node-tmp/pull/56) style(jshint): fix JSHint error ([@raszi](https://github.com/raszi))
* [#53](https://github.com/raszi/node-tmp/pull/53) chore: update license attribute ([@pdehaan](https://github.com/pdehaan))

#### Committers: 3
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- KARASZI István ([@raszi](https://github.com/raszi))
- Peter deHaan ([@pdehaan](https://github.com/pdehaan))


## v0.0.26 (2015-05-12)

#### :rocket: Enhancement
* [#40](https://github.com/raszi/node-tmp/pull/40) Fix for #39 ([@silkentrance](https://github.com/silkentrance))
* [#42](https://github.com/raszi/node-tmp/pull/42) Fix for #17 ([@silkentrance](https://github.com/silkentrance))
* [#41](https://github.com/raszi/node-tmp/pull/41) Fix for #37 ([@silkentrance](https://github.com/silkentrance))
* [#32](https://github.com/raszi/node-tmp/pull/32) add ability to customize file/dir names ([@shime](https://github.com/shime))
* [#29](https://github.com/raszi/node-tmp/pull/29) tmp.file have responsibility to close file, not only unlink file ([@vhain](https://github.com/vhain))

#### :bug: Bug Fix
* [#51](https://github.com/raszi/node-tmp/pull/51) fix(windows): fix tempDir on windows ([@raszi](https://github.com/raszi))
* [#49](https://github.com/raszi/node-tmp/pull/49) remove object from _removeObjects if cleanup fn is called Closes [#48](https://github.com/raszi/node-tmp/issues/48) ([@bmeck](https://github.com/bmeck))

#### :memo: Documentation
* [#45](https://github.com/raszi/node-tmp/pull/45) Fix for #44 ([@silkentrance](https://github.com/silkentrance))

#### :house: Internal
* [#34](https://github.com/raszi/node-tmp/pull/34) Create LICENSE ([@ScottWeinstein](https://github.com/ScottWeinstein))

#### Committers: 6
- Bradley Farias ([@bmeck](https://github.com/bmeck))
- Carsten Klein ([@silkentrance](https://github.com/silkentrance))
- Hrvoje Šimić ([@shime](https://github.com/shime))
- Juwan Yoo ([@vhain](https://github.com/vhain))
- KARASZI István ([@raszi](https://github.com/raszi))
- Scott Weinstein ([@ScottWeinstein](https://github.com/ScottWeinstein))


## v0.0.24 (2014-07-11)

#### :rocket: Enhancement
* [#25](https://github.com/raszi/node-tmp/pull/25) Added removeCallback passing ([@foxel](https://github.com/foxel))

#### Committers: 1
- Andrey Kupreychik ([@foxel](https://github.com/foxel))


## v0.0.23 (2013-12-03)

#### :rocket: Enhancement
* [#21](https://github.com/raszi/node-tmp/pull/21) If we are not on node 0.8, don't register an uncaughtException handler ([@wibblymat](https://github.com/wibblymat))

#### Committers: 1
- Mat Scales ([@wibblymat](https://github.com/wibblymat))


## v0.0.22 (2013-11-29)

#### :rocket: Enhancement
* [#19](https://github.com/raszi/node-tmp/pull/19) Rethrow only on node v0.8. ([@mcollina](https://github.com/mcollina))

#### Committers: 1
- Matteo Collina ([@mcollina](https://github.com/mcollina))


## v0.0.21 (2013-08-07)

#### :bug: Bug Fix
* [#16](https://github.com/raszi/node-tmp/pull/16) Fix bug where we delete contents of symlinks ([@lightsofapollo](https://github.com/lightsofapollo))

#### Committers: 1
- James Lal ([@lightsofapollo](https://github.com/lightsofapollo))


## v0.0.17 (2013-04-09)

#### :rocket: Enhancement
* [#9](https://github.com/raszi/node-tmp/pull/9) add recursive remove option ([@oscar-broman](https://github.com/oscar-broman))

#### Committers: 1
- [@oscar-broman](https://github.com/oscar-broman)


## v0.0.14 (2012-08-26)

#### :rocket: Enhancement
* [#5](https://github.com/raszi/node-tmp/pull/5) Export _getTmpName for temporary file name creation ([@joscha](https://github.com/joscha))

#### Committers: 1
- Joscha Feth ([@joscha](https://github.com/joscha))


## Previous Releases < v0.0.14

- no information available
