App<IAppOption>({
  globalData: {
    isLoggedIn: false,
    userInfo: null,
    openid: '',
    loginCallbacks: [],
    // 全局统计数据缓存
    statsCache: null as any,
    statsCacheTime: 0,
    statsCacheValidTime: 5 * 60 * 1000 // 5分钟缓存
  },

  onLaunch() {
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    wx.login({
      success: res => {
        console.log(res.code)
      },
    })
  },

  setGlobalLoginStatus(isLoggedIn: boolean, userInfo: UserInfo | null = null, openid: string = '') {
    this.globalData.isLoggedIn = isLoggedIn;
    this.globalData.userInfo = userInfo;
    this.globalData.openid = openid;
    this.globalData.loginCallbacks.forEach(callback => {
      callback(isLoggedIn, userInfo);
    });
  },

  onLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    this.globalData.loginCallbacks.push(callback);
  },

  offLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    const index = this.globalData.loginCallbacks.indexOf(callback);
    if (index > -1) {
      this.globalData.loginCallbacks.splice(index, 1);
    }
  },

  // 获取全局统计数据缓存
  getGlobalStatsCache(openid: string) {
    const now = Date.now();
    if (this.globalData.statsCache &&
        this.globalData.statsCache.openid === openid &&
        (now - this.globalData.statsCacheTime) < this.globalData.statsCacheValidTime) {
      return this.globalData.statsCache.data;
    }
    return null;
  },

  // 设置全局统计数据缓存
  setGlobalStatsCache(openid: string, data: any) {
    this.globalData.statsCache = {
      openid,
      data
    };
    this.globalData.statsCacheTime = Date.now();
  },

  // 清除全局统计数据缓存
  clearGlobalStatsCache() {
    this.globalData.statsCache = null;
    this.globalData.statsCacheTime = 0;
  }
})