// 云函数入口文件 - 定时更新排行榜
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('定时任务开始：更新排行榜');

  try {
    const startTime = Date.now();

    // 调用updateRankings云函数
    const result = await cloud.callFunction({
      name: 'updateRankings',
      data: {
        types: ['total', 'consecutive', 'duration'],
        isScheduled: true
      }
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`定时任务完成，耗时: ${duration}ms`);

    return {
      success: result.result.success,
      message: '定时更新排行榜成功',
      duration: duration,
      results: result.result.results,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('定时更新排行榜失败', error);
    return {
      success: false,
      error: error.message || '定时更新排行榜失败',
      timestamp: new Date().toISOString()
    };
  }
}


