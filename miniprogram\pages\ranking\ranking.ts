import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess, handleUserLogin } from '../../utils/auth';
import { db, userCollection, UserInfo, getRanking, getUserRank, UserRankInfo, updateRankings } from '../../utils/cloud';

interface RankingItem {
  id: number;
  nickname: string;
  userId: string;
  avatar: string;
  value: number;
  _id?: string;
  _openid?: string;
}

Page({
  data: {
    safeAreaTop: 0,
    // 当前选中的榜单类型：total(总天数), consecutive(连续天数), duration(总时长)
    currentTab: "total",
    // 排行榜数据
    rankingList: [] as RankingItem[],
    // 我的排名数据
    myRank: {
      rank: 0,
      nickname: "",
      userId: "",
      avatar: "/images/default-avatar.png",
      value: 0
    },
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this, () => {
      this.loadRankingData(this.data.currentTab);
    });
    this.customCheckLoginStatus();
  },

  onShow() {
    this.customCheckLoginStatus();
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },

  async customCheckLoginStatus() {
    await checkLoginStatus(this);
    this.loadRankingData(this.data.currentTab);
  },

  switchTab(e: any) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
    this.loadRankingData(tab);
  },
  
  async showLoginPopup() {
    await handleUserLogin(this);
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
    this.loadRankingData(this.data.currentTab);
  },

  // 加载排行榜数据
  async loadRankingData(type: string) {
    this.setData({ loading: true });

    try {
      // 从云数据库获取排行榜数据
      const rankingData = await this.getRankingFromCloud(type);

      // 如果已登录，获取用户排名
      let myRank = {
        rank: 0,
        nickname: "我的昵称",
        userId: "",
        avatar: "/images/default-avatar.png",
        value: 0
      };

      if (this.data.isLoggedIn && this.data.userInfo) {
        myRank = await this.getUserRanking(type);
      }

      this.setData({
        rankingList: rankingData,
        myRank: myRank
      });
    } catch (error) {
      console.error('加载排行榜数据失败', error);

      // 显示错误提示
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      });

      // 如果云数据库加载失败，使用模拟数据作为备选
      const mockData = this.getMockRankingData(type);
      this.setData({
        rankingList: mockData.list,
        myRank: mockData.myRank
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.checkLoginStatus();
    wx.stopPullDownRefresh();
  },

  // 重新加载数据
  retryLoadData() {
    this.loadRankingData(this.data.currentTab);
  },

  // 从云数据库获取排行榜数据
  async getRankingFromCloud(type: string): Promise<RankingItem[]> {
    try {
      console.log(`获取${type}排行榜数据`);

      // 调用新的getRanking云函数获取预计算的排行榜数据
      const rankingData = await getRanking(type, 50);

      console.log(`获取到${rankingData.length}条排行榜数据`);

      // 转换为页面需要的格式
      const rankingList: RankingItem[] = rankingData.map((item: any) => ({
        id: item.id,
        nickname: item.nickname,
        userId: item.userId,
        avatar: item.avatar,
        value: item.value,
        _openid: item._openid
      }));

      return rankingList;
    } catch (error) {
      console.error('获取排行榜数据失败', error);
      throw error;
    }
  },

  // 获取用户排名
  async getUserRanking(type: string): Promise<any> {
    try {
      if (!this.data.userInfo || !this.data.openid) {
        return {
          rank: 0,
          nickname: "我的昵称",
          userId: "",
          avatar: "/images/default-avatar.png",
          value: 0
        };
      }

      console.log(`获取用户${type}排名`);

      // 调用新的getUserRank云函数获取实时排名
      const userRank = await getUserRank(type, this.data.openid);

      if (userRank) {
        console.log(`用户排名获取成功: rank=${userRank.rank}, value=${userRank.value}`);
        return userRank;
      } else {
        console.log('用户排名获取失败，返回默认值');
        return {
          rank: 0,
          nickname: this.data.userInfo.nickName || '我的昵称',
          userId: this.data.userInfo.userId || '',
          avatar: this.data.userInfo.avatarUrl || '/images/default-avatar.png',
          value: 0
        };
      }
    } catch (error) {
      console.error('获取用户排名失败', error);
      return {
        rank: 0,
        nickname: this.data.userInfo?.nickName || "我的昵称",
        userId: this.data.userInfo?.userId || "",
        avatar: this.data.userInfo?.avatarUrl || "/images/default-avatar.png",
        value: 0
      };
    }
  },
  
  // 获取模拟数据
  getMockRankingData(tab: string) {
    // 模拟的用户头像
    const avatars = [
      "/images/default-avatar.png",
      "/images/default-avatar.png",
      "/images/default-avatar.png",
      "/images/default-avatar.png",
      "/images/default-avatar.png"
    ];
    
    // 模拟的用户昵称
    const nicknames = [
      "健身达人",
      "养生专家",
      "太极高手",
      "八段锦爱好者",
      "运动达人",
      "早起锻炼",
      "健康生活",
      "每日打卡王",
      "坚持就是胜利",
      "生命在于运动"
    ];
    
    let list: RankingItem[] = [];
    
    // 根据当前Tab生成不同的数据
    for (let i = 0; i < 20; i++) {
      let value = 0;
      
      if (tab === "total") {
        // 总打卡天数 - 降序排列
        value = 300 - i * 12 + Math.floor(Math.random() * 10);
      } else if (tab === "consecutive") {
        // 连续打卡天数 - 降序排列
        value = 100 - i * 4 + Math.floor(Math.random() * 5);
      } else {
        // 总时长 - 降序排列
        value = Math.round((150 - i * 6 + Math.floor(Math.random() * 8)) / 10);
      }
      
      list.push({
        id: i + 1,
        avatar: avatars[i % avatars.length],
        nickname: nicknames[i % nicknames.length],
        userId: "10" + (1000 + i),
        value: value
      });
    }
    
    // 我的排名信息
    const myRank = {
      rank: 16,
      nickname: "我的昵称",
      userId: "12345",
      avatar: "/images/default-avatar.png",
      value: tab === "duration" ? 34 : (tab === "consecutive" ? 21 : 78)
    };
    
    return {
      list,
      myRank
    };
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadRankingData(this.data.currentTab).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 手动刷新排行榜
  async refreshRanking() {
    wx.showLoading({
      title: '刷新中...'
    });

    try {
      await this.loadRankingData(this.data.currentTab);
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 初始化排行榜数据（开发调试用）
  async initRankingData() {
    wx.showLoading({
      title: '初始化排行榜...'
    });

    try {
      console.log('开始初始化排行榜数据');

      // 调用updateRankings云函数初始化所有排行榜
      const success = await updateRankings(['total', 'consecutive', 'duration']);

      if (success) {
        console.log('排行榜初始化成功');
        wx.showToast({
          title: '初始化成功',
          icon: 'success',
          duration: 2000
        });

        // 重新加载排行榜数据
        await this.loadRankingData(this.data.currentTab);
      } else {
        throw new Error('初始化失败');
      }
    } catch (error) {
      console.error('初始化排行榜失败', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
    }
  }
})