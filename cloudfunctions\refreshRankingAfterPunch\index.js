// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const userCollection = db.collection('users')
const punchRecordsCollection = db.collection('punchRecords')
const rankingsCollection = db.collection('rankings')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { openid } = event
  
  const targetOpenid = openid || wxContext.OPENID
  
  if (!targetOpenid) {
    return {
      success: false,
      error: '无法获取用户标识'
    }
  }

  try {
    console.log(`打卡后刷新排名: openid=${targetOpenid}`)
    
    // 获取用户信息
    const userResult = await userCollection
      .where({
        _openid: targetOpenid
      })
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      }
    }

    const userInfo = userResult.data[0]
    
    // 计算用户的最新统计数据
    const userStats = await calculateUserStats(targetOpenid)
    
    // 更新三个排行榜中的用户数据
    const types = ['total', 'consecutive', 'duration']
    const results = {}
    
    for (const type of types) {
      try {
        const result = await updateUserRankingInType(type, targetOpenid, userInfo, userStats)
        results[type] = result
      } catch (error) {
        console.error(`更新${type}排行榜失败:`, error)
        results[type] = { success: false, error: error.message }
      }
    }
    
    console.log('打卡后排名刷新完成:', results)
    
    return {
      success: true,
      message: '排名刷新成功',
      results: results
    }
  } catch (error) {
    console.error('打卡后刷新排名失败', error)
    return {
      success: false,
      error: error.message || '刷新排名失败'
    }
  }
}

// 更新用户在指定类型排行榜中的数据
async function updateUserRankingInType(type, openid, userInfo, userStats) {
  try {
    let value = 0
    switch (type) {
      case 'total':
        value = userStats.totalDays
        break
      case 'consecutive':
        value = userStats.consecutiveDays
        break
      case 'duration':
        value = Math.round(userStats.totalDuration / 60) // 转换为分钟
        break
    }
    
    // 如果用户数据为0，从排行榜中移除
    if (value === 0) {
      await rankingsCollection
        .where({
          type: type,
          openid: openid
        })
        .remove()
      
      return {
        success: true,
        action: 'removed',
        message: `用户从${type}排行榜中移除`
      }
    }
    
    // 查找用户在当前排行榜中的位置
    const existingRecord = await rankingsCollection
      .where({
        type: type,
        openid: openid
      })
      .get()
    
    // 计算新的排名
    const higherRecords = await rankingsCollection
      .where({
        type: type,
        value: db.command.gt(value)
      })
      .count()
    
    const newRank = higherRecords.total + 1
    
    const recordData = {
      type: type,
      rank: newRank,
      openid: openid,
      userId: userInfo.userId || '',
      nickName: userInfo.nickName || '匿名用户',
      avatarUrl: userInfo.avatarUrl || '/images/default-avatar.png',
      value: value,
      updatedAt: db.serverDate()
    }
    
    if (existingRecord.data.length > 0) {
      // 更新现有记录
      await rankingsCollection
        .doc(existingRecord.data[0]._id)
        .update({
          data: recordData
        })
      
      // 如果新排名在前50名，需要重新调整其他用户的排名
      if (newRank <= 50) {
        await adjustRankingsAfterUpdate(type, newRank, value, openid)
      }
      
      return {
        success: true,
        action: 'updated',
        rank: newRank,
        value: value,
        message: `用户在${type}排行榜中排名更新为第${newRank}名`
      }
    } else {
      // 新增记录（只有在前50名才添加）
      if (newRank <= 50) {
        await rankingsCollection.add({
          data: recordData
        })
        
        await adjustRankingsAfterUpdate(type, newRank, value, openid)
        
        return {
          success: true,
          action: 'added',
          rank: newRank,
          value: value,
          message: `用户进入${type}排行榜第${newRank}名`
        }
      } else {
        return {
          success: true,
          action: 'not_in_top50',
          rank: newRank,
          value: value,
          message: `用户排名第${newRank}名，未进入前50名`
        }
      }
    }
  } catch (error) {
    console.error(`更新${type}排行榜失败:`, error)
    throw error
  }
}

// 调整排名（简化版本，实际应用中可能需要更复杂的逻辑）
async function adjustRankingsAfterUpdate(type, newRank, newValue, excludeOpenid) {
  try {
    // 获取所有该类型的排行榜记录
    const allRecords = await rankingsCollection
      .where({
        type: type
      })
      .orderBy('value', 'desc')
      .get()
    
    // 重新计算排名
    const sortedRecords = allRecords.data
      .filter(record => record.openid !== excludeOpenid)
      .sort((a, b) => b.value - a.value)
    
    // 批量更新排名
    const updates = []
    sortedRecords.forEach((record, index) => {
      const correctRank = index + 1 + (newValue > record.value ? 1 : 0)
      if (record.rank !== correctRank) {
        updates.push(
          rankingsCollection.doc(record._id).update({
            data: { rank: correctRank }
          })
        )
      }
    })
    
    if (updates.length > 0) {
      await Promise.all(updates)
      console.log(`${type}排行榜排名调整完成，更新了${updates.length}条记录`)
    }
  } catch (error) {
    console.error('调整排名失败:', error)
  }
}

// 计算用户统计数据（复用逻辑）
async function calculateUserStats(openid) {
  try {
    const punchRecords = await punchRecordsCollection
      .where({
        _openid: openid
      })
      .orderBy('date', 'asc')
      .get()

    if (punchRecords.data.length === 0) {
      return {
        totalDays: 0,
        consecutiveDays: 0,
        totalDuration: 0
      }
    }

    const records = punchRecords.data
    
    // 计算总天数（去重日期）
    const uniqueDates = new Set(records.map(record => record.date))
    const totalDays = uniqueDates.size
    
    // 计算总时长（秒）
    const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    
    // 计算连续天数
    const consecutiveDays = calculateConsecutiveDays(Array.from(uniqueDates).sort())
    
    return {
      totalDays,
      consecutiveDays,
      totalDuration
    }
  } catch (error) {
    console.error('计算用户统计数据失败', error)
    throw error
  }
}

// 计算连续打卡天数
function calculateConsecutiveDays(sortedDates) {
  if (sortedDates.length === 0) return 0
  
  const today = new Date()
  const todayStr = formatDate(today)
  const yesterdayStr = formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000))
  
  // 检查今天或昨天是否有打卡记录
  const hasRecentPunch = sortedDates.includes(todayStr) || sortedDates.includes(yesterdayStr)
  if (!hasRecentPunch) {
    return 0
  }
  
  let consecutiveDays = 0
  let currentDate = new Date(today)
  
  // 从今天开始往前计算连续天数
  while (true) {
    const dateStr = formatDate(currentDate)
    if (sortedDates.includes(dateStr)) {
      consecutiveDays++
      currentDate.setDate(currentDate.getDate() - 1)
    } else {
      break
    }
  }
  
  return consecutiveDays
}

// 格式化日期为 YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
