// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const userCollection = db.collection('users')
const punchRecordsCollection = db.collection('punchRecords')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { type = 'total', openid } = event
  
  const targetOpenid = openid || wxContext.OPENID
  
  if (!targetOpenid) {
    return {
      success: false,
      error: '无法获取用户标识'
    }
  }

  if (!['total', 'consecutive', 'duration'].includes(type)) {
    return {
      success: false,
      error: '无效的排行榜类型'
    }
  }

  try {
    console.log(`获取用户排名: openid=${targetOpenid}, type=${type}`)
    
    // 获取用户基本信息
    const userResult = await userCollection
      .where({
        _openid: targetOpenid
      })
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        error: '用户不存在'
      }
    }

    const userInfo = userResult.data[0]
    
    // 计算用户的统计数据
    const userStats = await calculateUserStats(targetOpenid)
    
    // 根据类型获取对应的数值
    let userValue = 0
    switch (type) {
      case 'total':
        userValue = userStats.totalDays
        break
      case 'consecutive':
        userValue = userStats.consecutiveDays
        break
      case 'duration':
        userValue = Math.round(userStats.totalDuration / 60) // 转换为分钟
        break
    }

    // 计算排名：统计比当前用户数值更高的用户数量
    const higherUsersCount = await calculateHigherUsersCount(type, userValue)
    const rank = higherUsersCount + 1

    console.log(`用户排名计算完成: value=${userValue}, rank=${rank}`)

    return {
      success: true,
      rank: rank,
      nickname: userInfo.nickName || '匿名用户',
      userId: userInfo.userId || '',
      avatar: userInfo.avatarUrl || '/images/default-avatar.png',
      value: userValue
    }
  } catch (error) {
    console.error('获取用户排名失败', error)
    return {
      success: false,
      error: error.message || '获取用户排名失败'
    }
  }
}

// 计算用户统计数据
async function calculateUserStats(openid) {
  try {
    // 获取所有打卡记录，按日期排序
    const punchRecords = await punchRecordsCollection
      .where({
        _openid: openid
      })
      .orderBy('date', 'asc')
      .get()

    if (punchRecords.data.length === 0) {
      return {
        totalDays: 0,
        consecutiveDays: 0,
        totalDuration: 0
      }
    }

    const records = punchRecords.data
    
    // 计算总天数（去重日期）
    const uniqueDates = new Set(records.map(record => record.date))
    const totalDays = uniqueDates.size
    
    // 计算总时长（秒）
    const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    
    // 计算连续天数
    const consecutiveDays = calculateConsecutiveDays(Array.from(uniqueDates).sort())
    
    return {
      totalDays,
      consecutiveDays,
      totalDuration
    }
  } catch (error) {
    console.error('计算用户统计数据失败', error)
    throw error
  }
}

// 计算连续打卡天数
function calculateConsecutiveDays(sortedDates) {
  if (sortedDates.length === 0) return 0
  
  const today = new Date()
  const todayStr = formatDate(today)
  const yesterdayStr = formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000))
  
  // 检查今天或昨天是否有打卡记录
  const hasRecentPunch = sortedDates.includes(todayStr) || sortedDates.includes(yesterdayStr)
  if (!hasRecentPunch) {
    return 0 // 如果今天和昨天都没有打卡，连续天数为0
  }
  
  let consecutiveDays = 0
  let currentDate = new Date(today)
  
  // 从今天开始往前计算连续天数
  while (true) {
    const dateStr = formatDate(currentDate)
    if (sortedDates.includes(dateStr)) {
      consecutiveDays++
      currentDate.setDate(currentDate.getDate() - 1)
    } else {
      break
    }
  }
  
  return consecutiveDays
}

// 格式化日期为 YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算比当前用户数值更高的用户数量
async function calculateHigherUsersCount(type, userValue) {
  try {
    // 获取所有用户的openid
    const allUsersResult = await userCollection.get()
    const allUsers = allUsersResult.data
    
    let higherCount = 0
    
    // 为每个用户计算统计数据并比较
    for (const user of allUsers) {
      const userStats = await calculateUserStats(user._openid)
      
      let compareValue = 0
      switch (type) {
        case 'total':
          compareValue = userStats.totalDays
          break
        case 'consecutive':
          compareValue = userStats.consecutiveDays
          break
        case 'duration':
          compareValue = Math.round(userStats.totalDuration / 60)
          break
      }
      
      if (compareValue > userValue) {
        higherCount++
      }
    }
    
    return higherCount
  } catch (error) {
    console.error('计算排名失败', error)
    return 0
  }
}
