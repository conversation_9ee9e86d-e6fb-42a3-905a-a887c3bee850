/// <reference path="./types/index.d.ts" />
/// <reference path="./wx-api.d.ts" />

interface UserInfo {
  avatarUrl: string;
  nickName: string;
  signature: string;
  userId?: string;
  gender?: string;
  age?: number;
}

interface UserStats {
  totalDays: number;
  consecutiveDays: number;
  totalDuration: number;
}

interface IAppOption {
  globalData: {
    isLoggedIn: boolean;
    userInfo: UserInfo | null;
    openid: string;
    loginCallbacks: Array<(isLoggedIn: boolean, userInfo: UserInfo | null) => void>;
    statsCache: any;
    statsCacheTime: number;
    statsCacheValidTime: number;
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
  setGlobalLoginStatus?: (isLoggedIn: boolean, userInfo: UserInfo | null, openid: string) => void;
  onLoginStatusChange?: (callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) => void;
  offLoginStatusChange?: (callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) => void;
  getGlobalStatsCache?: (openid: string) => any;
  setGlobalStatsCache?: (openid: string, data: any) => void;
  clearGlobalStatsCache?: () => void;
}