{"$ref": "#/definitions/TsConfigSchema", "$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"TsConfigOptions": {"description": "Must be an interface to support `typescript-json-schema`.", "properties": {"compiler": {"default": "typescript", "description": "Specify a custom TypeScript compiler.", "type": "string"}, "compilerHost": {"default": false, "description": "Use TypeScript's compiler host API.", "type": "boolean"}, "compilerOptions": {"additionalProperties": true, "allOf": [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json#definitions/compilerOptionsDefinition/properties/compilerOptions"}], "description": "JSON object to merge with compiler options.", "properties": {}, "type": "object"}, "emit": {"default": false, "description": "Emit output files into `.ts-node` directory.", "type": "boolean"}, "files": {"default": false, "description": "Load files from `tsconfig.json` on startup.", "type": "boolean"}, "ignore": {"default": "/node_modules/", "description": "Override the path patterns to skip compilation.", "items": {"type": "string"}, "type": "array"}, "ignoreDiagnostics": {"description": "Ignore TypeScript warnings by diagnostic code.", "items": {"type": ["string", "number"]}, "type": "array"}, "logError": {"default": false, "description": "Logs TypeScript errors to st<PERSON><PERSON> instead of throwing exceptions.", "type": "boolean"}, "preferTsExts": {"default": false, "description": "Re-order file extensions so that TypeScript imports are preferred.", "type": "boolean"}, "pretty": {"default": false, "description": "Use pretty diagnostic formatter.", "type": "boolean"}, "scope": {"default": false, "description": "<PERSON>ope compiler to files within `cwd`.", "type": "boolean"}, "skipIgnore": {"default": false, "description": "<PERSON><PERSON> ignore check.", "type": "boolean"}, "transpileOnly": {"default": false, "description": "Use TypeScript's faster `transpileModule`.", "type": "boolean"}, "typeCheck": {"default": true, "description": "**DEPRECATED** Specify type-check is enabled (e.g. `transpileOnly == false`).", "type": "boolean"}}, "type": "object"}, "TsConfigSchema": {"allOf": [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json"}], "description": "tsconfig schema which includes \"ts-node\" options.", "properties": {"ts-node": {"$ref": "#/definitions/TsConfigOptions", "description": "ts-node options.  See also: https://github.com/TypeStrong/ts-node#configuration-options\n\nts-node offers TypeScript execution and REPL for node.js, with source map support."}}, "type": "object"}}}