import { db, userCollection, UserInfo, getOpenid, getUserInfoFromCloud, generateUserId } from '../../utils/cloud';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    avatarUrl: '/images/tou.png',
    nickname: '',
    hasChosenAvatar: false,
    openid: '',
    loading: false,
    showPrivacyPopup: false,
    privacyContractName: '',
    needPrivacyAuthorization: false,
    privacyResolve: null as any
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 组件初始化
    attached() {
      this.initPrivacySettings();
      this.setupPrivacyListener();
    },

    // 初始化隐私设置
    async initPrivacySettings() {
      try {
        // 设置默认的隐私协议名称
        this.setData({
          privacyContractName: '《用户隐私保护指引》'
        });
      } catch (error) {
        console.error('初始化隐私设置失败:', error);
      }
    },

    // 设置隐私监听器
    setupPrivacyListener() {
      // 隐私监听器会在需要时自动触发
      console.log('隐私监听器已设置');
    },

    // 关闭弹窗
    closePopup() {
      this.setData({
        show: false
      });
      this.triggerEvent('close');
    },

    // 阻止冒泡
    preventBubbling() {
      // 防止点击内容区域时关闭弹窗
      return;
    },

    // 选择头像
    async onChooseAvatar(e: any) {
      try {
        const { avatarUrl } = e.detail;

        // 检查文件是否存在
        if (!avatarUrl) {
          wx.showToast({
            title: '头像选择失败',
            icon: 'none'
          });
          return;
        }

        // 处理头像文件
        if (avatarUrl.includes('tmp')) {
          // 临时文件，上传到云存储
          try {
            const cloudPath = `avatars/${Date.now()}-${Math.random().toString(36).substring(2, 11)}.jpg`;
            const uploadResult = await wx.cloud.uploadFile({
              cloudPath,
              filePath: avatarUrl
            });

            if (uploadResult.fileID) {
              this.setData({
                avatarUrl: uploadResult.fileID,
                hasChosenAvatar: true
              });
            } else {
              throw new Error('上传失败');
            }
          } catch (uploadError) {
            console.warn('云存储上传失败:', uploadError);
            this.setData({
              avatarUrl,
              hasChosenAvatar: true
            });
          }
        } else {
          // 非临时文件，直接使用
          this.setData({
            avatarUrl,
            hasChosenAvatar: true
          });
        }

        wx.showToast({
          title: '头像选择成功',
          icon: 'success',
          duration: 1000
        });

      } catch (error) {
        console.error('选择头像失败:', error);
        wx.showToast({
          title: '头像选择失败，请重试',
          icon: 'none'
        });
      }
    },

    // 输入昵称
    onInputNickname(e: any) {
      this.setData({
        nickname: e.detail.value
      });
    },

    // 使用微信头像和昵称
    useWechatInfo() {
      wx.showToast({
        title: '此功能正在开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 关闭隐私弹窗
    closePrivacyPopup() {
      this.setData({
        showPrivacyPopup: false
      });
      // 如果有待处理的resolve，拒绝授权
      if (this.data.privacyResolve) {
        this.data.privacyResolve({ event: 'disagree' });
        this.setData({
          privacyResolve: null
        });
      }

      // 用户拒绝隐私协议，提示可以手动填写
      wx.showToast({
        title: '您可以手动上传头像和填写昵称',
        icon: 'none',
        duration: 2000
      });
    },

    // 打开隐私协议页面
    openPrivacyContract() {
      // 由于API限制，这里显示一个模拟的隐私协议内容
      wx.showModal({
        title: '用户隐私保护指引',
        content: '我们将严格按照相关法律法规保护您的个人信息，仅在必要时收集和使用您的头像、昵称等信息，用于完善用户体验。',
        showCancel: false,
        confirmText: '知道了'
      });
    },

    // 同意隐私协议
    agreePrivacyAuthorization() {
      // 处理resolve回调
      if (this.data.privacyResolve) {
        this.data.privacyResolve({ buttonId: 'agree-privacy-btn', event: 'agree' });
        this.setData({
          privacyResolve: null
        });
      }

      // 关闭隐私弹窗
      this.setData({
        showPrivacyPopup: false,
        needPrivacyAuthorization: false
      });

      // 提示用户可以使用微信头像昵称填写能力
      wx.showToast({
        title: '请点击头像选择，并填写昵称',
        icon: 'none',
        duration: 2000
      });
    },



    // 获取完整的用户信息
    async getCompleteUserInfo(): Promise<UserInfo | null> {
      try {
        if (!this.data.openid) {
          return null;
        }

        const queryResult = await userCollection.where({
          _openid: this.data.openid
        }).get();

        if (queryResult.data.length > 0) {
          const userData = queryResult.data[0];
          return {
            avatarUrl: userData.avatarUrl,
            nickName: userData.nickName,
            signature: userData.signature || '坚持，是一种力量',
            userId: userData.userId || '',
            totalDays: userData.totalDays || 0,
            consecutiveDays: userData.consecutiveDays || 0,
            totalDuration: userData.totalDuration || 0
          };
        }
        return null;
      } catch (error) {
        console.error('获取完整用户信息失败', error);
        return null;
      }
    },

    // 保存用户信息到云数据库
    async saveUserToCloud(userInfo: UserInfo): Promise<void> {
      try {
        // 查询用户是否已存在
        const queryResult = await userCollection.where({
          _openid: this.data.openid
        }).get();

        if (queryResult.data.length === 0) {
          const userId = await generateUserId();
          await userCollection.add({
            data: {
              ...userInfo,
              userId,
              totalDays: 0,
              consecutiveDays: 0,
              totalDuration: 0,
              createdAt: db.serverDate(),
              updatedAt: db.serverDate()
            }
          });
        } else {
          // 已有用户，更新信息
          const docId = queryResult.data[0]._id;
          if (docId) {
            await userCollection.doc(docId).update({
              data: {
                ...userInfo,
                updatedAt: db.serverDate()
              }
            });
          }
        }
      } catch (error) {
        console.error('保存用户信息失败', error);
        throw error;
      }
    },

    // 保存并登录
    async saveAndLogin() {
      // 验证头像是否已选择
      if (!this.data.hasChosenAvatar) {
        wx.showModal({
          title: '提示',
          content: '请先选择头像，您可以点击头像区域选择图片，或点击"使用微信头像和昵称"按钮',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      if (!this.data.nickname.trim()) {
        wx.showModal({
          title: '提示',
          content: '请输入昵称，这将作为您在小程序中的显示名称',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      // 验证昵称长度
      if (this.data.nickname.trim().length > 20) {
        wx.showToast({
          title: '昵称不能超过20个字符',
          icon: 'none'
        });
        return;
      }

      this.setData({ loading: true });

      try {
        const openid = await getOpenid();
        if (!openid) {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          return;
        }
        this.setData({ openid });

        // 构建用户信息
        const userInfo: UserInfo = {
          avatarUrl: this.data.avatarUrl,
          nickName: this.data.nickname.trim(),
          signature: '坚持，是一种力量'
        };

        // 保存到云数据库
        await this.saveUserToCloud(userInfo);

        const completeUserInfo = await getUserInfoFromCloud(this.data.openid);
        const finalUserInfo = completeUserInfo || {
          ...userInfo,
          userId: '',
          totalDays: 0,
          consecutiveDays: 0,
          totalDuration: 0
        };

        // 保存完整信息到本地存储
        wx.setStorageSync('userInfo', finalUserInfo);

        // 更新全局登录状态
        const app = getApp<IAppOption>();
        if (app.setGlobalLoginStatus) {
          app.setGlobalLoginStatus(true, finalUserInfo, this.data.openid);
        }

        // 触发保存成功事件，传递完整数据给父组件
        this.triggerEvent('save', {
          userInfo: finalUserInfo,
          openid: this.data.openid
        });

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 关闭弹窗
        this.closePopup();

      } catch (error) {
        console.error('登录失败', error);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    }
  }
})