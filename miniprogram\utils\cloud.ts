const CLOUD_ENV = 'cloud1-5go7er9y38b6f1bd';

wx.cloud.init({
  env: CLOUD_ENV,
  traceUser: true
});

export const db = wx.cloud.database();
export const userCollection = db.collection('users');
export const punchRecordsCollection = db.collection('punchRecords');

export interface UserInfo {
  avatarUrl: string;
  nickName: string;
  signature: string;
  userId?: string;
  gender?: string;
  age?: number;
}

export interface UserStats {
  totalDays: number;
  consecutiveDays: number;
  totalDuration: number;
}

export interface CloudFunctionResult {
  result?: {
    openid?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface PunchRecord {
  _id?: string;
  _openid?: string;
  date: string;
  duration: number;
  createdAt?: any;
}

export const getOpenid = async (): Promise<string | null> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getOpenid'
    }) as CloudFunctionResult;
    return res.result?.openid || null;
  } catch (error) {
    console.error('获取openid失败', error);
    return null;
  }
};

export const getUserInfoFromCloud = async (openid: string): Promise<UserInfo | null> => {
  try {
    const queryResult = await userCollection.where({
      _openid: openid
    }).get();
    return queryResult.data.length > 0 ? queryResult.data[0] as UserInfo : null;
  } catch (error) {
    console.error('获取用户信息失败', error);
    return null;
  }
};

export const generateUserId = async (): Promise<string> => {
  try {
    const result = await userCollection.orderBy('userId', 'desc').limit(1).get();
    if (result.data.length > 0) {
      const lastUserId = result.data[0].userId || '00000000';
      const nextId = parseInt(lastUserId) + 1;
      return nextId.toString().padStart(8, '0');
    }
    return '00000001';
  } catch (error) {
    console.error('生成用户ID失败', error);
    return Date.now().toString().slice(-8).padStart(8, '0');
  }
};

export const getUserStats = async (openid: string): Promise<UserStats | null> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'getUserStats',
      data: { openid }
    }) as CloudFunctionResult;
    return res.result as UserStats || null;
  } catch (error) {
    console.error('获取用户统计数据失败', error);
    return null;
  }
};

export const savePunchRecord = async (date: string, duration: number): Promise<boolean> => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'savePunchRecord',
      data: { date, duration }
    }) as CloudFunctionResult;
    return res.result?.success || false;
  } catch (error) {
    console.error('保存打卡记录失败', error);
    return false;
  }
};
