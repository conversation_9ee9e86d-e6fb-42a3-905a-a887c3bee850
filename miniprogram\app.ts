App<IAppOption>({
  globalData: {
    isLoggedIn: false,
    userInfo: null,
    openid: '',
    loginCallbacks: [],
    // 全局统计数据缓存
    statsCache: null as any,
    statsCacheTime: 0,
    statsCacheValidTime: 5 * 60 * 1000 // 5分钟缓存
  },

  onLaunch() {
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    wx.login({
      success: res => {
        console.log(res.code)
      },
    })

    // 自动初始化排行榜
    this.initRankingsOnLaunch();
  },

  // 自动初始化排行榜
  async initRankingsOnLaunch() {
    try {
      console.log('小程序启动：开始检查排行榜初始化状态');

      // 检查是否已经初始化过
      const lastInitTime = wx.getStorageSync('rankings_last_init');
      const now = Date.now();
      const oneDay = 24 * 60 * 60 * 1000; // 24小时

      // 如果超过24小时没有初始化，或者从未初始化过，则进行初始化
      if (!lastInitTime || (now - lastInitTime) > oneDay) {
        console.log('需要初始化排行榜，开始调用云函数');

        // 动态导入云函数
        const { updateRankings } = await import('./utils/cloud');

        // 调用云函数初始化排行榜
        const success = await updateRankings(['total', 'consecutive', 'duration']);

        if (success) {
          console.log('排行榜自动初始化成功');
          wx.setStorageSync('rankings_last_init', now);
        } else {
          console.log('排行榜自动初始化失败，将在下次启动时重试');
        }
      } else {
        console.log('排行榜已在24小时内初始化过，跳过本次初始化');
      }
    } catch (error) {
      console.error('排行榜自动初始化出错:', error);
      // 静默失败，不影响小程序正常启动
    }
  },

  setGlobalLoginStatus(isLoggedIn: boolean, userInfo: UserInfo | null = null, openid: string = '') {
    this.globalData.isLoggedIn = isLoggedIn;
    this.globalData.userInfo = userInfo;
    this.globalData.openid = openid;
    this.globalData.loginCallbacks.forEach(callback => {
      callback(isLoggedIn, userInfo);
    });
  },

  onLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    this.globalData.loginCallbacks.push(callback);
  },

  offLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    const index = this.globalData.loginCallbacks.indexOf(callback);
    if (index > -1) {
      this.globalData.loginCallbacks.splice(index, 1);
    }
  },

  // 获取全局统计数据缓存
  getGlobalStatsCache(openid: string) {
    const now = Date.now();
    if (this.globalData.statsCache &&
        this.globalData.statsCache.openid === openid &&
        (now - this.globalData.statsCacheTime) < this.globalData.statsCacheValidTime) {
      return this.globalData.statsCache.data;
    }
    return null;
  },

  // 设置全局统计数据缓存
  setGlobalStatsCache(openid: string, data: any) {
    this.globalData.statsCache = {
      openid,
      data
    };
    this.globalData.statsCacheTime = Date.now();
  },

  // 清除全局统计数据缓存
  clearGlobalStatsCache() {
    this.globalData.statsCache = null;
    this.globalData.statsCacheTime = 0;
  }
})