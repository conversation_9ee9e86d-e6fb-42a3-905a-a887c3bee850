// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const userCollection = db.collection('users');
const rankingsCollection = db.collection('rankings');

// 内置工具函数（从rankingUtils复制）
const rankingUtils = {
  /**
   * 计算用户统计数据
   * @param {string} openid 用户openid
   * @returns {Object} 统计数据 {totalDays, consecutiveDays, totalDuration}
   */
  async calculateUserStats(openid) {
    try {
      // 获取所有打卡记录，按日期排序
      const punchRecords = await db.collection('punchRecords')
        .where({
          _openid: openid
        })
        .orderBy('date', 'asc')
        .get();

      if (punchRecords.data.length === 0) {
        return {
          totalDays: 0,
          consecutiveDays: 0,
          totalDuration: 0
        };
      }

      const records = punchRecords.data;

      // 计算总天数（去重日期）
      const uniqueDates = new Set(records.map(record => record.date));
      const totalDays = uniqueDates.size;

      // 计算总时长（秒）
      const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0);

      // 计算连续天数
      const consecutiveDays = this.calculateConsecutiveDays(Array.from(uniqueDates).sort());

      return {
        totalDays,
        consecutiveDays,
        totalDuration
      };
    } catch (error) {
      console.error('计算用户统计数据失败', error);
      throw error;
    }
  },

  /**
   * 计算连续打卡天数
   * @param {Array} sortedDates 排序后的日期数组
   * @returns {number} 连续天数
   */
  calculateConsecutiveDays(sortedDates) {
    if (sortedDates.length === 0) return 0;

    const today = new Date();
    const todayStr = this.formatDate(today);
    const yesterdayStr = this.formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000));

    // 检查今天或昨天是否有打卡记录
    const hasRecentPunch = sortedDates.includes(todayStr) || sortedDates.includes(yesterdayStr);
    if (!hasRecentPunch) {
      return 0; // 如果今天和昨天都没有打卡，连续天数为0
    }

    let consecutiveDays = 0;
    let currentDate = new Date(today);

    // 从今天开始往前计算连续天数
    while (true) {
      const dateStr = this.formatDate(currentDate);
      if (sortedDates.includes(dateStr)) {
        consecutiveDays++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }

    return consecutiveDays;
  },

  /**
   * 格式化日期为 YYYY-MM-DD
   * @param {Date} date 日期对象
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 获取用户数值（根据排行榜类型）
   * @param {Object} stats 用户统计数据
   * @param {string} type 排行榜类型
   * @returns {number} 用户数值
   */
  getUserValueByType(stats, type) {
    switch (type) {
      case 'total':
        return stats.totalDays;
      case 'consecutive':
        return stats.consecutiveDays;
      case 'duration':
        return Math.round(stats.totalDuration / 60); // 转换为分钟
      default:
        return 0;
    }
  }
};

// 云函数入口函数
exports.main = async (event, context) => {
  const { types = ['total', 'consecutive', 'duration'], isScheduled = false } = event;

  console.log(`开始${isScheduled ? '定时' : '手动'}更新排行榜:`, types);

  try {
    const startTime = Date.now();
    const results = {};

    for (const type of types) {
      console.log(`更新${type}排行榜`);
      const result = await updateRankingByType(type);
      results[type] = result;
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`排行榜更新完成，耗时: ${duration}ms`, results);

    return {
      success: true,
      message: `${isScheduled ? '定时' : '手动'}更新排行榜成功`,
      results: results,
      duration: duration,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('更新排行榜失败', error);
    return {
      success: false,
      error: error.message || '更新排行榜失败',
      timestamp: new Date().toISOString()
    };
  }
}

// 按类型更新排行榜
async function updateRankingByType(type) {
  try {
    // 获取所有用户
    const allUsersResult = await userCollection.get();
    const allUsers = allUsersResult.data;

    console.log(`开始计算${allUsers.length}个用户的${type}排行榜`);

    // 计算所有用户的统计数据
    const userStats = [];
    let processedCount = 0;

    for (const user of allUsers) {
      try {
        const stats = await rankingUtils.calculateUserStats(user._openid);
        const value = rankingUtils.getUserValueByType(stats, type);

        // 只包含有数据的用户
        if (value > 0) {
          userStats.push({
            openid: user._openid,
            userId: user.userId || '',
            nickName: user.nickName || '匿名用户',
            avatarUrl: user.avatarUrl || '/images/default-avatar.png',
            value: value
          });
        }

        processedCount++;

        // 每处理50个用户输出一次进度
        if (processedCount % 50 === 0) {
          console.log(`${type}排行榜计算进度: ${processedCount}/${allUsers.length}`);
        }
      } catch (error) {
        console.error(`计算用户${user._openid}统计数据失败:`, error);
        // 继续处理其他用户
      }
    }

    // 按数值降序排序
    userStats.sort((a, b) => b.value - a.value);

    // 只取前50名
    const top50 = userStats.slice(0, 50);

    console.log(`${type}排行榜计算完成，前50名用户数量: ${top50.length}`);

    // 清除旧的排行榜数据
    const removeResult = await rankingsCollection.where({
      type: type
    }).remove();

    console.log(`清除${type}排行榜旧数据: ${removeResult.stats.removed}条`);

    // 批量插入新的排行榜数据
    let insertedCount = 0;
    if (top50.length > 0) {
      const rankingRecords = top50.map((user, index) => ({
        type: type,
        rank: index + 1,
        openid: user.openid,
        userId: user.userId,
        nickName: user.nickName,
        avatarUrl: user.avatarUrl,
        value: user.value,
        updatedAt: db.serverDate()
      }));

      // 分批插入（每批最多20条，避免超时）
      const batchSize = 20;
      for (let i = 0; i < rankingRecords.length; i += batchSize) {
        const batch = rankingRecords.slice(i, i + batchSize);
        const insertPromises = batch.map(record =>
          rankingsCollection.add({ data: record })
        );
        await Promise.all(insertPromises);
        insertedCount += batch.length;

        console.log(`${type}排行榜插入进度: ${insertedCount}/${rankingRecords.length}`);
      }
    }

    return {
      success: true,
      type: type,
      processedUsers: processedCount,
      validUsers: userStats.length,
      top50Count: top50.length,
      removedCount: removeResult.stats.removed,
      insertedCount: insertedCount,
      message: `${type}排行榜更新成功`
    };
  } catch (error) {
    console.error(`更新${type}排行榜失败:`, error);
    return {
      success: false,
      type: type,
      error: error.message
    };
  }
}


