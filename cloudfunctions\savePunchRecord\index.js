// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const punchRecordsCollection = db.collection('punchRecords')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { date, duration } = event
  
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      error: '无法获取用户标识'
    }
  }

  if (!date || !duration) {
    return {
      success: false,
      error: '缺少必要参数：date 或 duration'
    }
  }

  // 验证duration是否为有效数字
  const durationNum = Number(duration)
  if (isNaN(durationNum) || durationNum < 0) {
    return {
      success: false,
      error: '练习时长必须为有效的正数'
    }
  }

  // 如果练习时长少于5秒，不保存记录
  if (durationNum < 5) {
    return {
      success: false,
      error: '练习时长不足5秒，不保存记录'
    }
  }

  try {
    console.log(`查询条件: openid=${openid}, date=${date}`)

    // 检查今日是否已有记录
    const existingRecord = await punchRecordsCollection
      .where({
        _openid: openid,
        date: date
      })
      .get()

    console.log(`查询结果: 找到${existingRecord.data.length}条记录`)
    if (existingRecord.data.length > 0) {
      console.log('现有记录详情:', existingRecord.data[0])
    }

    if (existingRecord.data.length > 0) {
      // 更新现有记录
      const record = existingRecord.data[0]
      const recordId = record._id
      const existingDuration = record.duration || 0
      const newTotalDuration = existingDuration + durationNum

      console.log(`更新现有记录: recordId=${recordId}, 原时长=${existingDuration}秒, 新增=${durationNum}秒, 总计=${newTotalDuration}秒`)

      await punchRecordsCollection.doc(recordId).update({
        data: {
          duration: newTotalDuration,
          updatedAt: db.serverDate()
        }
      })

      console.log('记录更新成功')

      return {
        success: true,
        message: '打卡记录更新成功',
        data: {
          recordId: recordId,
          date: date,
          duration: newTotalDuration,
          isNewRecord: false
        }
      }
    } else {
      // 创建新记录
      console.log(`创建新记录: date=${date}, duration=${durationNum}秒`)

      const result = await punchRecordsCollection.add({
        data: {
          _openid: openid,  // 关键修复：添加用户openid
          date: date,
          duration: durationNum,
          createdAt: db.serverDate(),
          updatedAt: db.serverDate()
        }
      })

      console.log(`新记录创建成功: recordId=${result._id}`)

      return {
        success: true,
        message: '打卡记录创建成功',
        data: {
          recordId: result._id,
          date: date,
          duration: durationNum,
          isNewRecord: true
        }
      }
    }
  } catch (error) {
    console.error('保存打卡记录失败', error)
    return {
      success: false,
      error: error.message || '保存打卡记录失败'
    }
  }
}
