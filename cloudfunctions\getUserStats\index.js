// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const userCollection = db.collection('users')
const punchRecordsCollection = db.collection('punchRecords')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { openid } = event
  
  // 如果没有传入openid，使用当前用户的openid
  const targetOpenid = openid || wxContext.OPENID
  
  if (!targetOpenid) {
    return {
      success: false,
      error: '无法获取用户标识'
    }
  }

  try {
    // 从打卡记录计算统计数据
    const stats = await calculateUserStats(targetOpenid)
    
    return {
      success: true,
      totalDays: stats.totalDays,
      consecutiveDays: stats.consecutiveDays,
      totalDuration: stats.totalDuration
    }
  } catch (error) {
    console.error('获取用户统计数据失败', error)
    return {
      success: false,
      error: error.message || '获取统计数据失败'
    }
  }
}

// 计算用户统计数据
async function calculateUserStats(openid) {
  try {
    // 获取所有打卡记录，按日期排序
    const punchRecords = await punchRecordsCollection
      .where({
        _openid: openid
      })
      .orderBy('date', 'asc')
      .get()

    if (punchRecords.data.length === 0) {
      return {
        totalDays: 0,
        consecutiveDays: 0,
        totalDuration: 0
      }
    }

    const records = punchRecords.data
    
    // 计算总天数（去重日期）
    const uniqueDates = new Set(records.map(record => record.date))
    const totalDays = uniqueDates.size
    
    // 计算总时长（秒）
    const totalDuration = records.reduce((sum, record) => sum + (record.duration || 0), 0)
    
    // 计算连续天数
    const consecutiveDays = calculateConsecutiveDays(Array.from(uniqueDates).sort())
    
    return {
      totalDays,
      consecutiveDays,
      totalDuration
    }
  } catch (error) {
    console.error('计算用户统计数据失败', error)
    throw error
  }
}

// 计算连续打卡天数
function calculateConsecutiveDays(sortedDates) {
  if (sortedDates.length === 0) return 0
  
  const today = new Date()
  const todayStr = formatDate(today)
  const yesterdayStr = formatDate(new Date(today.getTime() - 24 * 60 * 60 * 1000))
  
  // 检查今天或昨天是否有打卡记录
  const hasRecentPunch = sortedDates.includes(todayStr) || sortedDates.includes(yesterdayStr)
  if (!hasRecentPunch) {
    return 0 // 如果今天和昨天都没有打卡，连续天数为0
  }
  
  let consecutiveDays = 0
  let currentDate = new Date(today)
  
  // 从今天开始往前计算连续天数
  while (true) {
    const dateStr = formatDate(currentDate)
    if (sortedDates.includes(dateStr)) {
      consecutiveDays++
      currentDate.setDate(currentDate.getDate() - 1)
    } else {
      break
    }
  }
  
  return consecutiveDays
}

// 格式化日期为 YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
